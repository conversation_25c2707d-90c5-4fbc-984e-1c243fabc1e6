import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学公式字体

# =============================================================================
# 配置区域 - 可自定义设置
# =============================================================================
# 输出图片文件夹设置
Endpoint = 'AlgAT'  # FishAT, FishCT, DMCT, DMAT, AlgAT
Task = 'AROC'         # 'n' , 'R2', 'RMSE', 'MAE, 'F1', 'AROC', 'RBA'
Wtmethod = 'exp'     # 'rigid', 'exp'
OUTPUT_DIR = f'AD_{Endpoint}'  # 图片输出文件夹名称

# 确保输出文件夹存在
def ensure_output_dir(output_dir):
    """确保输出文件夹存在，如果不存在则创建"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出文件夹: {output_dir}")
    return output_dir

# 创建输出文件夹
output_folder = ensure_output_dir(OUTPUT_DIR)

"""
读取与 AD_figure.py 相同位置的输入文件：
左列热力图：来自 OUTPUT_DIR/model_{Wtmethod}_AD_{Task}.csv
右列热力图：来自 OUTPUT_DIR/model_{Wtmethod}_AD_n.csv
"""

# 左列指标矩阵
metric_file = f"model_{Wtmethod}_AD_{Task}.csv"
metric_path = os.path.join(OUTPUT_DIR, metric_file)
df_metric = pd.read_csv(metric_path, index_col=0)
print(f"成功读取指标矩阵: {metric_path}")

# 右列 N（样本数量）矩阵，固定读取 n.csv
n_file = f"model_{Wtmethod}_AD_n.csv"
n_path = os.path.join(OUTPUT_DIR, n_file)
df_n = pd.read_csv(n_path, index_col=0)
print(f"成功读取 N 矩阵: {n_path}")

# 与 AD_figure.py 对齐：x 轴为 columns (ρ_s)，y 轴为 index (I_A)
x_labels = df_metric.columns.astype(float).values # ρ_s (横轴)
y_labels = df_metric.index.astype(float).values   # I_A (纵轴)

# 不需要转置，直接使用原始矩阵
# imshow 的列对应 x_labels (ρ_s)，行对应 y_labels (I_A)
metric_matrix = df_metric.values
n_matrix = df_n.values

# 找到AROC矩阵的最大值位置（最优组合）
# max_pos = np.unravel_index(np.nanargmax(metric_matrix), metric_matrix.shape)
max_pos = np.unravel_index(np.nanargmax(n_matrix), n_matrix.shape)
max_row, max_col = max_pos
optimal_IA = y_labels[max_row]  # 最优 I_A
optimal_rho = x_labels[max_col]  # 最优 ρ_s
max_aroc = metric_matrix[max_row, max_col]  # 最优 AROC 值
max_n = n_matrix[max_row, max_col]  # 对应的 N 值

fig, axes = plt.subplots(nrows=2, ncols=1, figsize=(8, 10),
                        gridspec_kw={'height_ratios': [1, 1]})

# 辅助函数：根据边缘自适应偏移，避免数值注释越界
def place_label(ax, col, row, text, ncols, nrows):
    near_left = col <= 0.5
    near_right = col >= ncols - 1 - 0.5
    near_top = row >= nrows - 1 - 0.5
    near_bottom = row <= 0.5

    # 水平方向
    if near_left:
        dx, ha = 8, 'left'
    elif near_right:
        dx, ha = -8, 'right'
    else:
        dx, ha = 0, 'center'

    # 垂直方向
    if near_top:
        dy, va = -8, 'top'
    else:
        dy, va = 8, 'bottom'

    ax.annotate(text, (col, row), xytext=(dx, dy), textcoords='offset points',
                ha=ha, va=va, fontsize=14, weight='bold', color='black', clip_on=True)

# 上方：AROC 热力图
im1 = axes[0].imshow(metric_matrix, origin='lower', aspect='auto', cmap='Greens',
                     vmin=np.nanmin(metric_matrix), vmax=np.nanmax(metric_matrix))
axes[0].set_ylabel('$I_{\mathrm{A,T}}$', fontsize=14, rotation=0, labelpad=20)
axes[0].set_xticks(range(len(x_labels)))
def fmt_tick(x):
    if x == 0:
        return '0'
    if abs(x) < 1e-2:
        s = f"{x:.0e}"  # 例如 '1e-05'
        base, exp = s.split('e')
        exp = int(exp)
        # 注意：使用单反斜杠的 \times，且 $ 内不要多余空格
        return rf'${base}\times10^{{{exp}}}$'
    return f"{x:.2f}"
axes[0].set_xticklabels([fmt_tick(x) for x in x_labels], fontsize=14, rotation=0)
axes[0].set_yticks(range(len(y_labels)))
axes[0].set_yticklabels(['{:.2f}'.format(y) for y in y_labels], fontsize=14)
# 在最优位置标记星星和数值（自适应偏移）
axes[0].scatter(max_col, max_row, color='gold', s=200, marker='*', zorder=10, edgecolors='gold', linewidth=2)
place_label(axes[0], max_col, max_row, f'{max_aroc:.3f}', ncols=metric_matrix.shape[1], nrows=metric_matrix.shape[0])

# 下方：N_AD 热力图
im2 = axes[1].imshow(n_matrix, origin='lower', aspect='auto', cmap='Blues',
                     vmin=np.nanmin(n_matrix), vmax=np.nanmax(n_matrix))
axes[1].set_ylabel('$I_{\mathrm{A,T}}$', fontsize=14, rotation=0, labelpad=20)
axes[1].set_xticks(range(len(x_labels)))
axes[1].set_xticklabels([fmt_tick(x) for x in x_labels], fontsize=14, rotation=0)
axes[1].set_yticks(range(len(y_labels)))
axes[1].set_yticklabels(['{:.2f}'.format(y) for y in y_labels], fontsize=14)
# 在相同位置标记星星和N值（自适应偏移）
axes[1].scatter(max_col, max_row, color='gold', s=200, marker='*', zorder=10, edgecolors='gold', linewidth=2)
place_label(axes[1], max_col, max_row, f'{int(max_n)}', ncols=n_matrix.shape[1], nrows=n_matrix.shape[0])

# 统一设置 x 轴标签（只有下方图显示）
axes[1].set_xlabel('$ρ_{\mathrm{s,T}}$', fontsize=14)

# 色条
cbar1 = plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
cbar1.set_label(r'$A_{\rm ROC}$', fontsize=14, rotation=0, labelpad=15)
cbar1.ax.tick_params(labelsize=14)
cbar2 = plt.colorbar(im2, ax=axes[1], fraction=0.046, pad=0.04)
cbar2.set_label(r'$N_{AD}$', fontsize=14, rotation=0, labelpad=15)
cbar2.ax.tick_params(labelsize=14)

# 在图的角落标注 Endpoint 名称（默认左上角，可切换为右上角）
corner = 'right'  # 可选：'left' 或 'right'
if corner == 'left':
    axes[0].text(0.01, 0.98, Endpoint, transform=axes[0].transAxes,
                 ha='left', va='top', fontsize=14, weight='bold')
else:
    axes[0].text(0.99, 0.98, Endpoint, transform=axes[0].transAxes,
                 ha='right', va='top', fontsize=14, weight='bold')

plt.tight_layout()
output_file_path = os.path.join(output_folder, f'AD_{Endpoint}_heatmap_{Wtmethod}_AD_{Task}.png')
plt.savefig(output_file_path, dpi=300, bbox_inches='tight')
print(f"图片已保存到: {output_file_path}")
plt.show()