import torch
import dgl
from rdkit import Chem
from rdkit.Chem import MolFromSmiles
from utils.MY_GNN import MGA  
import numpy as np
import os
import pandas as pd

# 设备配置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 新增参数配置
args = {
    'select_task_list': ['FishLC50'],  # 添加任务名称列表
    'atom_data_field': 'atom',
    'bond_data_field': 'etype',
    'explicit_H': False,  # 与训练时保持一致
    'use_chirality': True  # 与训练时保持一致
}

def one_of_k_atompair_encoding(x, allowable_set):
    for atompair in allowable_set:
        if x in atompair:
            x = atompair
            break
        else:
            if atompair == allowable_set[-1]:
                x = allowable_set[-1]
            else:
                continue
    return [x == s for s in allowable_set]

def one_of_k_encoding(x, allowable_set):
    if x not in allowable_set:
        raise Exception("input {0} not in allowable set{1}:".format(
            x, allowable_set))
    return [x == s for s in allowable_set]

def one_of_k_encoding_unk(x, allowable_set):
    """Maps inputs not in the allowable set to the last element."""
    if x not in allowable_set:
        x = allowable_set[-1]
    return [x == s for s in allowable_set]

def load_trained_model(model_path, device=device):
    """加载预训练模型"""
    model_args = {
        'in_feats': 40,
        'rgcn_hidden_feats': [128, 128],
        'n_tasks': 1,
        'classifier_hidden_feats': 128,
        'rgcn_drop_out': 0.2,
        'dropout': 0.2,
        'loop': True
    }
    
    model = MGA(**model_args)
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device).eval()
    return model

# 添加缺失的etype_features函数
def etype_features(bond, use_chirality=True, atompair=True):
    """边类型特征计算（必须与训练时一致）"""
    bt = bond.GetBondType()
    bond_feats_1 = [
        bt == Chem.rdchem.BondType.SINGLE, 
        bt == Chem.rdchem.BondType.DOUBLE,
        bt == Chem.rdchem.BondType.TRIPLE, 
        bt == Chem.rdchem.BondType.AROMATIC,
    ]
    a = next((i for i, m in enumerate(bond_feats_1) if m), 0)

    bond_feats_2 = bond.GetIsConjugated()
    b = 1 if bond_feats_2 else 0

    bond_feats_3 = bond.IsInRing()
    c = 1 if bond_feats_3 else 0

    index = a * 1 + b * 4 + c * 8
    
    if use_chirality:
        bond_feats_4 = one_of_k_encoding_unk(
            str(bond.GetStereo()),
            ["STEREONONE", "STEREOANY", "STEREOZ", "STEREOE"])
        d = next((i for i, m in enumerate(bond_feats_4) if m), 0)
        index += d * 16
    
    if atompair:
        atom_pair_str = bond.GetBeginAtom().GetSymbol() + bond.GetEndAtom().GetSymbol()
        bond_feats_5 = one_of_k_atompair_encoding(
            atom_pair_str, 
            [['CC'], ['CN', 'NC'], ['ON', 'NO'], ['CO', 'OC'], ['CS', 'SC'],
             ['SO', 'OS'], ['NN'], ['SN', 'NS'], ['CCl', 'ClC'], ['CF', 'FC'],
             ['CBr', 'BrC'], ['others']]
        )
        e = next((i for i, m in enumerate(bond_feats_5) if m), 0)
        index += e * 64
    
    return index



def atom_features(atom):
    """原子特征（修复参数传递）"""
    return atom_features_impl(
        atom, 
        explicit_H=args['explicit_H'],
        use_chirality=args['use_chirality']
    )

def atom_features_impl(atom, explicit_H, use_chirality):
    """实际的原子特征实现"""
    # 保持原有实现不变
    results = one_of_k_encoding_unk(
        atom.GetSymbol(),
        ['B','C','N','O','F','Si','P','S','Cl','As','Se','Br','Te','I','At','other']
    ) + one_of_k_encoding(atom.GetDegree(), [0,1,2,3,4,5,6]) + \
    [atom.GetFormalCharge(), atom.GetNumRadicalElectrons()] + \
    one_of_k_encoding_unk(atom.GetHybridization(), [
        Chem.rdchem.HybridizationType.SP, 
        Chem.rdchem.HybridizationType.SP2,
        Chem.rdchem.HybridizationType.SP3, 
        Chem.rdchem.HybridizationType.SP3D,
        Chem.rdchem.HybridizationType.SP3D2,'other'
    ]) + [atom.GetIsAromatic()]
    
    if not explicit_H:
        results += one_of_k_encoding_unk(atom.GetTotalNumHs(), [0,1,2,3,4])
    
    if use_chirality:
        try:
            results += one_of_k_encoding_unk(atom.GetProp('_CIPCode'), ['R','S']) + \
                      [atom.HasProp('_ChiralityPossible')]
        except:
            results += [False, False] + [atom.HasProp('_ChiralityPossible')]
    
    return np.array(results)

def construct_molecule_graph(smiles):
    """修复图构造"""
    mol = MolFromSmiles(smiles)
    if mol is None:
        raise ValueError(f"Invalid SMILES: {smiles}")
    
    g = dgl.DGLGraph()
    num_atoms = mol.GetNumAtoms()
    g.add_nodes(num_atoms)
    
    # 原子特征
    atom_features_list = [atom_features(atom) for atom in mol.GetAtoms()]
    g.ndata[args['atom_data_field']] = torch.tensor(
        np.array(atom_features_list),
        dtype=torch.float32
    )
    
    # 边特征
    src, dst, etypes = [], [], []
    for bond in mol.GetBonds():
        u = bond.GetBeginAtomIdx()
        v = bond.GetEndAtomIdx()
        # 添加双向边
        src.extend([u, v])
        dst.extend([v, u])
        # 计算边类型
        etype = etype_features(bond)
        etypes.extend([etype, etype])
    
    g.add_edges(src, dst)
    g.edata[args['bond_data_field']] = torch.tensor(etypes, dtype=torch.long)
    return g

def predict_smiles(model, smiles_list, batch_size=128):
    """修复批量预测"""
    all_preds = []
    
    for i in range(0, len(smiles_list), batch_size):
        batch_smiles = smiles_list[i:i+batch_size]
        try:
            graphs = []
            for smi in batch_smiles:
                try:
                    graphs.append(construct_molecule_graph(smi))
                except ValueError as e:
                    print(f"Invalid SMILES: {smi}, {str(e)}")
                    graphs.append(dgl.DGLGraph())  # 添加空图占位
            
            # 过滤无效图
            valid_graphs = [g for g in graphs if g.num_nodes() > 0]
            if not valid_graphs:
                all_preds.extend([np.nan]*len(batch_smiles))
                continue
            
            bg = dgl.batch(valid_graphs).to(device)
            atom_feats = bg.ndata[args['atom_data_field']].to(device)
            bond_feats = bg.edata[args['bond_data_field']].to(device)
            
            with torch.no_grad():
                batch_pred = model(bg, atom_feats, bond_feats).cpu().numpy()
            
            # 处理无效预测
            pred_idx = 0
            final_preds = []
            for g in graphs:
                if g.num_nodes() > 0:
                    pred_value = batch_pred[pred_idx][0]  # 添加[0]解包标量
                    final_preds.append(pred_value)
                    pred_idx += 1
                else:
                    final_preds.append(np.nan)
            all_preds.extend(final_preds)
            
        except Exception as e:
            print(f"Batch error: {str(e)}")
            all_preds.extend([np.nan]*len(batch_smiles))
    
    return np.array(all_preds)

def _graph_to_feature_dict(g, atom_field='atom', bond_field='etype'):
    """Convert a DGLGraph into a plain feature dict for saving."""
    if g.num_nodes() == 0:
        return None
    atom_feats = g.ndata[atom_field].cpu().numpy()
    try:
        src, dst = g.edges(order='eid')
    except TypeError:
        src, dst = g.edges()
    src = src.cpu().numpy()
    dst = dst.cpu().numpy()
    etype = g.edata[bond_field].cpu().numpy()
    return {
        'num_nodes': g.num_nodes(),
        'num_edges': g.num_edges(),
        'atom_features': atom_feats,
        'edge_index': np.stack([src, dst], axis=0),
        'edge_type': etype,
    }


def _ensure_dir(path: str):
    if path and not os.path.exists(path):
        os.makedirs(path, exist_ok=True)


def predict_and_save_smiles(model, smiles_list, batch_size=128,
                            save_features=False,
                            features_out_dir='prediction/features',
                            features_prefix='sample',
                            index_csv_path='prediction/features_index.csv'):
    """
    Predict and optionally save per-SMILES graph input features (not fingerprints).
    Returns (predictions: np.ndarray, index_csv_path or None)
    """
    all_preds = []
    saved_records = []
    counter = 0

    if save_features:
        _ensure_dir(features_out_dir)
        _ensure_dir(os.path.dirname(index_csv_path))

    for i in range(0, len(smiles_list), batch_size):
        batch_smiles = smiles_list[i:i+batch_size]
        try:
            graphs = []
            for smi in batch_smiles:
                try:
                    g = construct_molecule_graph(smi)
                    graphs.append(g)
                except ValueError as e:
                    print(f"Invalid SMILES: {smi}, {str(e)}")
                    graphs.append(dgl.DGLGraph())

            valid_graphs = [g for g in graphs if g.num_nodes() > 0]
            if valid_graphs:
                bg = dgl.batch(valid_graphs).to(device)
                atom_feats = bg.ndata[args['atom_data_field']].to(device)
                bond_feats = bg.edata[args['bond_data_field']].to(device)
                with torch.no_grad():
                    batch_pred = model(bg, atom_feats, bond_feats).cpu().numpy()
            else:
                batch_pred = np.array([]).reshape(0, 1)

            pred_idx = 0
            final_preds = []
            for j, g in enumerate(graphs):
                smi = batch_smiles[j]
                if g.num_nodes() > 0:
                    pred_value = batch_pred[pred_idx][0]
                    final_preds.append(pred_value)
                    pred_idx += 1
                    if save_features:
                        feat = _graph_to_feature_dict(g, args['atom_data_field'], args['bond_data_field'])
                        if feat is not None:
                            fname = f"{features_prefix}_{counter:06d}.npz"
                            fpath = os.path.join(features_out_dir, fname)
                            np.savez_compressed(fpath, **feat)
                            saved_records.append({
                                'index': counter,
                                'smiles': smi,
                                'file': fpath,
                                'num_nodes': feat['num_nodes'],
                                'num_edges': feat['num_edges'],
                            })
                            counter += 1
                else:
                    final_preds.append(np.nan)
                    if save_features:
                        saved_records.append({
                            'index': counter,
                            'smiles': smi,
                            'file': '',
                            'num_nodes': 0,
                            'num_edges': 0,
                        })
                        counter += 1

            all_preds.extend(final_preds)
        except Exception as e:
            print(f"Batch error: {str(e)}")
            all_preds.extend([np.nan] * len(batch_smiles))

    if save_features:
        idx_df = pd.DataFrame(saved_records, columns=['index', 'smiles', 'file', 'num_nodes', 'num_edges'])
        idx_df.to_csv(index_csv_path, index=False)
        return np.array(all_preds), index_csv_path
    return np.array(all_preds), None


def export_model(model, export_path: str):
    """Export model weights with the same checkpoint structure as training."""
    _ensure_dir(os.path.dirname(export_path))
    torch.save({'model_state_dict': model.state_dict()}, export_path)
    return export_path

def build_model_from_checkpoint(model_path, return_mol_embedding=False, device=device):
    """根据checkpoint自动匹配结构参数并构建模型，支持返回图级嵌入。"""
    ckpt = torch.load(model_path, map_location=device)
    state = ckpt['model_state_dict'] if isinstance(ckpt, dict) and 'model_state_dict' in ckpt else ckpt

    # 推断 RGCN 层数与每层输出维度
    layer_outs = []
    i = 0
    while True:
        key = f'gnn_layers.{i}.bn_layer.weight'
        if key in state:
            layer_outs.append(state[key].shape[0])
            i += 1
        else:
            break
    if not layer_outs:
        # 备用：从 h_bias 推断
        i = 0
        while True:
            key = f'gnn_layers.{i}.graph_conv_layer.h_bias'
            if key in state:
                layer_outs.append(state[key].shape[0])
                i += 1
            else:
                break
    rgcn_hidden_feats = layer_outs if layer_outs else [128, 128]

    # 推断分类头数量（任务数）
    task_indices = set()
    for k in state.keys():
        if k.startswith('output_layer1.'):
            parts = k.split('.')
            if len(parts) > 1 and parts[1].isdigit():
                task_indices.add(int(parts[1]))
        elif k.startswith('weighted_sum_readout.atom_weighting_specific.'):
            parts = k.split('.')
            if len(parts) > 3 and parts[3].isdigit():
                task_indices.add(int(parts[3]))
    n_tasks = (max(task_indices) + 1) if task_indices else 1

    # 推断分类器隐藏维度
    clf_hidden = None
    for k, v in state.items():
        if k.startswith('fc_layers1.0.3.running_mean'):
            clf_hidden = v.shape[0]
            break
        if k.startswith('fc_layers1.0.1.weight'):
            clf_hidden = v.shape[0]
            break
    classifier_hidden_feats = int(clf_hidden) if clf_hidden is not None else 128

    model = MGA(
        in_feats=40,
        rgcn_hidden_feats=rgcn_hidden_feats,
        n_tasks=n_tasks,
        rgcn_drop_out=0.2,
        classifier_hidden_feats=classifier_hidden_feats,
        dropout=0.2,
        loop=True,
        return_mol_embedding=return_mol_embedding,
    )
    model.load_state_dict(state, strict=True)
    model.to(device).eval()
    return model


def build_embedding_model(model_path, device=device):
    """构建返回图级嵌入的模型（自动从checkpoint推断结构）。"""
    return build_model_from_checkpoint(model_path, return_mol_embedding=True, device=device)


def compute_gnn_embeddings(model, smiles_list, batch_size=128):
    """Compute fixed-length GNN graph embeddings for each SMILES.
    Returns np.ndarray of shape (len(smiles_list), D) with NaNs for invalid SMILES.
    """
    embeddings = None
    all_rows = []

    for i in range(0, len(smiles_list), batch_size):
        batch_smiles = smiles_list[i:i+batch_size]
        graphs = []
        valid_mask = []
        for smi in batch_smiles:
            try:
                g = construct_molecule_graph(smi)
                graphs.append(g)
                valid_mask.append(1)
            except Exception as e:
                print(f"Invalid SMILES: {smi}, {str(e)}")
                graphs.append(dgl.DGLGraph())
                valid_mask.append(0)
        valid_graphs = [g for g in graphs if g.num_nodes() > 0]
        if valid_graphs:
            bg = dgl.batch(valid_graphs).to(device)
            atom_feats = bg.ndata[args['atom_data_field']].to(device)
            bond_feats = bg.edata[args['bond_data_field']].to(device)
            with torch.no_grad():
                emb = model(bg, atom_feats, bond_feats).cpu().numpy()  # (n_valid, D)
        else:
            emb = np.zeros((0, 0), dtype=np.float32)

        # initialize embeddings matrix lazily once D is known
        if embeddings is None:
            # Use actual dim if available; otherwise fall back to model's readout in_feats
            if emb.size > 0:
                D = emb.shape[1]
            else:
                D = int(getattr(getattr(model, 'weighted_sum_readout', None), 'in_feats', 128))
            embeddings = np.full((len(smiles_list), D), np.nan, dtype=np.float32)

        # scatter back to absolute rows
        valid_idx = 0
        for j, smi in enumerate(batch_smiles):
            row = i + j
            if valid_mask[j] == 1:
                embeddings[row, :] = emb[valid_idx]
                valid_idx += 1
            # else remains NaN
    return embeddings


def save_gnn_features_to_csv(input_csv_path, output_csv_path, model_path, smiles_col='smiles', batch_size=128):
    """Append GNN feature columns GNN_1..GNN_n to the source CSV and save to output CSV."""
    df = pd.read_csv(input_csv_path)
    if smiles_col not in df.columns:
        raise KeyError(f"CSV缺少列: {smiles_col}")
    smi_list = df[smiles_col].astype(str).tolist()

    emb_model = build_embedding_model(model_path, device=device)
    embs = compute_gnn_embeddings(emb_model, smi_list, batch_size=batch_size)

    # add columns efficiently (avoid fragmentation)
    gnn_cols = [f"GNN_{i+1}" for i in range(embs.shape[1])]
    gnn_df = pd.DataFrame(embs, columns=gnn_cols)
    # align by row order and concatenate once
    df = pd.concat([df.reset_index(drop=True), gnn_df], axis=1, copy=False)

    # save
    _ensure_dir(os.path.dirname(output_csv_path))
    df.to_csv(output_csv_path, index=False)
    return output_csv_path

if __name__ == "__main__":
    # 仅处理输入CSV：在源文件基础上追加 GNN_1..GNN_n 列并保存
    model_path = os.path.join('model', 'MTL-scr_early_stop.pth')
    input_csv_path = os.path.join('prediction', 'TrainingSet_FishAT.csv')  # 请改为你的输入文件
    output_csv_path = os.path.join('prediction', 'TrainingSet_FishAT_with_gnn.csv')

    print("Generating GNN features and writing to CSV...")
    out_csv = save_gnn_features_to_csv(
        input_csv_path=input_csv_path,
        output_csv_path=output_csv_path,
        model_path=model_path,
        smiles_col='smiles',
        batch_size=128
    )
    print(f"已生成携带GNN特征的CSV: {out_csv}")
