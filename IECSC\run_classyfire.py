#!/usr/bin/env python3
"""
ClassyFire启动脚本

提供简单的命令行界面来运行ClassyFire分类任务。
"""

import sys
import os
from classyfire_main import ClassyFireBatchProcessor
from classyfire_monitor import monitor_existing_queries, collect_results_only
from test_classyfire import run_all_tests
import config
import logging

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT
)
logger = logging.getLogger(__name__)

def show_help():
    """显示帮助信息"""
    print("""
ClassyFire化学品分类工具

用法:
    python run_classyfire.py [命令] [选项]

命令:
    run         运行完整的分类流程（默认）
    monitor     检查已提交查询的状态
    collect     收集已完成查询的结果
    test        运行测试检查工具是否正常工作
    help        显示此帮助信息

选项:
    --input FILE     指定输入文件（默认: config.py中的设置）
    --output FILE    指定输出文件（默认: config.py中的设置）
    --column NAME    指定SMILES列名（默认: config.py中的设置）
    --batch-size N   指定批处理大小（默认: config.py中的设置）
    --delay N        指定提交延迟秒数（默认: config.py中的设置）

示例:
    python run_classyfire.py run
    python run_classyfire.py run --input my_data.xlsx --output results.xlsx
    python run_classyfire.py monitor
    python run_classyfire.py collect
    python run_classyfire.py test
""")

def run_classification(args):
    """运行分类任务"""
    # 解析参数
    input_file = config.INPUT_FILE
    output_file = config.OUTPUT_FILE
    smiles_column = config.SMILES_COLUMN
    batch_size = config.BATCH_SIZE
    delay = config.SUBMIT_DELAY
    
    i = 1
    while i < len(args):
        if args[i] == '--input' and i + 1 < len(args):
            input_file = args[i + 1]
            i += 2
        elif args[i] == '--output' and i + 1 < len(args):
            output_file = args[i + 1]
            i += 2
        elif args[i] == '--column' and i + 1 < len(args):
            smiles_column = args[i + 1]
            i += 2
        elif args[i] == '--batch-size' and i + 1 < len(args):
            batch_size = int(args[i + 1])
            i += 2
        elif args[i] == '--delay' and i + 1 < len(args):
            delay = float(args[i + 1])
            i += 2
        else:
            i += 1
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return False
    
    logger.info(f"输入文件: {input_file}")
    logger.info(f"输出文件: {output_file}")
    logger.info(f"SMILES列: {smiles_column}")
    logger.info(f"批处理大小: {batch_size}")
    logger.info(f"提交延迟: {delay}秒")
    
    # 创建处理器并运行
    processor = ClassyFireBatchProcessor(batch_size=batch_size, delay=delay)
    success = processor.process_excel_file(input_file, output_file, smiles_column)
    
    if success:
        logger.info("分类任务完成！")
        return True
    else:
        logger.error("分类任务失败！")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        command = "run"
        args = []
    else:
        command = sys.argv[1]
        args = sys.argv[1:]
    
    if command == "help" or command == "--help" or command == "-h":
        show_help()
    elif command == "run":
        run_classification(args)
    elif command == "monitor":
        monitor_existing_queries()
    elif command == "collect":
        collect_results_only()
    elif command == "test":
        run_all_tests()
    else:
        print(f"未知命令: {command}")
        print("使用 'python run_classyfire.py help' 查看帮助信息")

if __name__ == "__main__":
    main()
