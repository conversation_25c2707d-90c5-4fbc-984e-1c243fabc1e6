#!/usr/bin/env python3
"""
ClassyFire快速开始脚本

提供简单的交互式界面来运行ClassyFire分类任务。
"""

import os
import sys
import subprocess
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def find_excel_files():
    """查找当前目录下的Excel文件"""
    excel_files = []
    for file in os.listdir('.'):
        if file.endswith(('.xlsx', '.xls')):
            excel_files.append(file)
    return excel_files

def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("ClassyFire化学品分类工具 - 快速开始")
    print("="*50)
    print("1. 提交新的分类任务")
    print("2. 检查查询状态")
    print("3. 收集分类结果")
    print("4. 查看帮助")
    print("5. 退出")
    print("="*50)

def submit_task():
    """提交分类任务"""
    print("\n--- 提交分类任务 ---")
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    if not excel_files:
        print("当前目录下没有找到Excel文件")
        return
    
    print("找到以下Excel文件:")
    for i, file in enumerate(excel_files, 1):
        print(f"{i}. {file}")
    
    # 选择文件
    try:
        choice = int(input(f"请选择文件 (1-{len(excel_files)}): ")) - 1
        if choice < 0 or choice >= len(excel_files):
            print("无效选择")
            return
        
        input_file = excel_files[choice]
    except ValueError:
        print("无效输入")
        return
    
    # 输入参数
    smiles_column = input("SMILES列名 (默认: smiles): ").strip() or "smiles"
    
    try:
        batch_size = input("每批处理数量 (默认: 50): ").strip()
        batch_size = int(batch_size) if batch_size else 50
        
        delay = input("提交间隔秒数 (默认: 5): ").strip()
        delay = float(delay) if delay else 5.0
    except ValueError:
        print("参数输入错误，使用默认值")
        batch_size = 50
        delay = 5.0
    
    # 确认提交
    print(f"\n提交参数:")
    print(f"  文件: {input_file}")
    print(f"  SMILES列: {smiles_column}")
    print(f"  批处理大小: {batch_size}")
    print(f"  提交间隔: {delay}秒")
    
    confirm = input("\n确认提交? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消提交")
        return
    
    # 执行提交
    cmd = [sys.executable, "classyfire_submit.py", input_file, smiles_column, str(batch_size), str(delay)]
    
    try:
        print("\n开始提交...")
        result = subprocess.run(cmd, check=True)
        print("提交完成！")
    except subprocess.CalledProcessError as e:
        print(f"提交失败: {e}")
    except FileNotFoundError:
        print("未找到 classyfire_submit.py 文件")

def check_status():
    """检查查询状态"""
    print("\n--- 检查查询状态 ---")
    
    # 查找查询ID文件
    query_files = []
    for file in os.listdir('.'):
        if file.endswith('_query_ids.txt') or file.endswith('_query_info.json'):
            query_files.append(file)
    
    if not query_files:
        print("未找到查询ID文件")
        print("请先使用选项1提交分类任务")
        return
    
    print("找到以下查询文件:")
    for i, file in enumerate(query_files, 1):
        print(f"{i}. {file}")
    
    # 选择文件
    try:
        choice = int(input(f"请选择文件 (1-{len(query_files)}): ")) - 1
        if choice < 0 or choice >= len(query_files):
            print("无效选择")
            return
        
        query_file = query_files[choice]
    except ValueError:
        print("无效输入")
        return
    
    # 执行状态检查
    cmd = [sys.executable, "classyfire_collect.py", "status", query_file]
    
    try:
        print("\n检查状态...")
        result = subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"状态检查失败: {e}")
    except FileNotFoundError:
        print("未找到 classyfire_collect.py 文件")

def collect_results():
    """收集分类结果"""
    print("\n--- 收集分类结果 ---")
    
    # 查找查询ID文件
    query_files = []
    for file in os.listdir('.'):
        if file.endswith('_query_ids.txt') or file.endswith('_query_info.json'):
            query_files.append(file)
    
    if not query_files:
        print("未找到查询ID文件")
        print("请先使用选项1提交分类任务")
        return
    
    print("找到以下查询文件:")
    for i, file in enumerate(query_files, 1):
        print(f"{i}. {file}")
    
    # 选择文件
    try:
        choice = int(input(f"请选择文件 (1-{len(query_files)}): ")) - 1
        if choice < 0 or choice >= len(query_files):
            print("无效选择")
            return
        
        query_file = query_files[choice]
    except ValueError:
        print("无效输入")
        return
    
    # 输出文件名
    default_output = f"{os.path.splitext(query_file)[0]}_results.xlsx"
    output_file = input(f"输出文件名 (默认: {default_output}): ").strip() or default_output
    
    # 原始文件（可选）
    excel_files = find_excel_files()
    if excel_files:
        print("\n找到以下Excel文件（用于合并原始数据）:")
        print("0. 不使用原始文件")
        for i, file in enumerate(excel_files, 1):
            print(f"{i}. {file}")
        
        try:
            choice = int(input(f"请选择原始文件 (0-{len(excel_files)}): "))
            if choice == 0:
                original_file = None
            elif 1 <= choice <= len(excel_files):
                original_file = excel_files[choice - 1]
            else:
                print("无效选择，不使用原始文件")
                original_file = None
        except ValueError:
            print("无效输入，不使用原始文件")
            original_file = None
    else:
        original_file = None
    
    # 执行收集
    cmd = [sys.executable, "classyfire_collect.py", query_file, output_file]
    if original_file:
        cmd.append(original_file)
    
    try:
        print("\n开始收集结果...")
        print("注意: 此过程可能需要较长时间，程序会等待所有查询完成")
        result = subprocess.run(cmd, check=True)
        print("结果收集完成！")
    except subprocess.CalledProcessError as e:
        print(f"结果收集失败: {e}")
    except FileNotFoundError:
        print("未找到 classyfire_collect.py 文件")

def show_help():
    """显示帮助信息"""
    print("\n--- 帮助信息 ---")
    print("ClassyFire化学品分类工具使用说明:")
    print("")
    print("1. 提交新的分类任务:")
    print("   - 选择包含SMILES的Excel文件")
    print("   - 设置批处理参数")
    print("   - 提交到ClassyFire服务器")
    print("")
    print("2. 检查查询状态:")
    print("   - 查看已提交查询的处理状态")
    print("   - 了解完成进度")
    print("")
    print("3. 收集分类结果:")
    print("   - 从ClassyFire服务器下载结果")
    print("   - 合并到Excel文件中")
    print("   - 程序会等待所有查询完成")
    print("")
    print("注意事项:")
    print("- ClassyFire分类需要时间，复杂化合物可能需要几小时")
    print("- 建议先提交任务，稍后再收集结果")
    print("- 可以随时检查查询状态")
    print("- 确保网络连接稳定")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (1-5): ").strip()
            
            if choice == '1':
                submit_task()
            elif choice == '2':
                check_status()
            elif choice == '3':
                collect_results()
            elif choice == '4':
                show_help()
            elif choice == '5':
                print("退出程序")
                break
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n程序被中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
