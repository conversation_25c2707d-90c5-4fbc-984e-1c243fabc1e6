#!/usr/bin/env python3
"""
ClassyFire化学品分类工具

使用ClassyFire API对化学品进行自动分类，支持批量提交、监控和结果导出。
"""

import requests
import pandas as pd
import time
import json
import pickle
import os
from typing import Dict, List, Optional, Tuple
import logging
from urllib.parse import quote

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireClient:
    """ClassyFire API客户端"""

    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0',
            'Content-Type': 'application/json'
        })

    def submit_query(self, structures: List[str], query_type: str = "STRUCTURE") -> Optional[int]:
        """
        提交分类查询

        Args:
            structures: SMILES字符串列表
            query_type: 查询类型，默认为"STRUCTURE"

        Returns:
            查询ID，失败返回None
        """
        try:
            # 构建查询数据
            query_data = {
                "label": f"Batch_Classification_{int(time.time())}",
                "query_input": "\n".join(structures),
                "query_type": query_type
            }

            url = f"{self.base_url}/queries.json"
            response = self.session.post(url, json=query_data, timeout=30)

            if response.status_code == 201:
                result = response.json()
                query_id = result.get('id')
                logger.info(f"成功提交查询，ID: {query_id}")
                return query_id
            else:
                logger.error(f"提交查询失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"提交查询时发生错误: {e}")
            return None

    def get_query_status(self, query_id: int) -> Optional[Dict]:
        """
        获取查询状态

        Args:
            query_id: 查询ID

        Returns:
            查询状态信息，失败返回None
        """
        try:
            url = f"{self.base_url}/queries/{query_id}.json"
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询状态失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取查询状态时发生错误: {e}")
            return None

    def get_query_results(self, query_id: int, page: int = 1) -> Optional[Dict]:
        """
        获取查询结果（分页）

        Args:
            query_id: 查询ID
            page: 页码，从1开始

        Returns:
            查询结果，失败返回None
        """
        try:
            url = f"{self.base_url}/queries/{query_id}.json?page={page}"
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询结果失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取查询结果时发生错误: {e}")
            return None

    def get_entity_by_inchikey(self, inchikey: str) -> Optional[Dict]:
        """
        通过InChIKey获取已分类的化合物信息

        Args:
            inchikey: InChIKey

        Returns:
            化合物分类信息，失败返回None
        """
        try:
            url = f"{self.base_url}/entities/{inchikey}.json"
            response = self.session.get(url, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                return None

        except Exception as e:
            logger.error(f"获取化合物信息时发生错误: {e}")
            return None

class ClassyFireBatchProcessor:
    """ClassyFire批量处理器"""

    def __init__(self, batch_size: int = 100, delay: float = 2.0):
        """
        初始化批量处理器

        Args:
            batch_size: 每批处理的化合物数量
            delay: 提交间隔时间（秒）
        """
        self.client = ClassyFireClient()
        self.batch_size = batch_size
        self.delay = delay
        self.status_file = "classyfire_status.pkl"

    def load_status(self) -> Dict:
        """加载处理状态"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"加载状态文件失败: {e}")
        return {}

    def save_status(self, status: Dict):
        """保存处理状态"""
        try:
            with open(self.status_file, 'wb') as f:
                pickle.dump(status, f)
        except Exception as e:
            logger.error(f"保存状态文件失败: {e}")

    def _submit_batches(self, df: pd.DataFrame, smiles_column: str, status: Dict) -> List[int]:
        """
        分批提交SMILES进行分类

        Args:
            df: 包含SMILES的DataFrame
            smiles_column: SMILES列名
            status: 状态字典

        Returns:
            成功提交的查询ID列表
        """
        query_ids = status.get('query_ids', [])
        submitted_count = status.get('submitted_count', 0)

        # 如果已经提交过，直接返回查询ID
        if query_ids and submitted_count >= len(df):
            logger.info(f"已提交所有查询，共 {len(query_ids)} 个查询")
            return query_ids

        # 分批提交
        smiles_list = df[smiles_column].tolist()
        total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size

        for i in range(submitted_count // self.batch_size, total_batches):
            start_idx = i * self.batch_size
            end_idx = min((i + 1) * self.batch_size, len(smiles_list))
            batch_smiles = smiles_list[start_idx:end_idx]

            logger.info(f"提交第 {i+1}/{total_batches} 批，包含 {len(batch_smiles)} 个化合物")

            # 提交查询
            query_id = self.client.submit_query(batch_smiles)

            if query_id:
                query_ids.append(query_id)
                submitted_count = end_idx

                # 保存状态
                status['query_ids'] = query_ids
                status['submitted_count'] = submitted_count
                self.save_status(status)

                logger.info(f"成功提交查询 {query_id}，已提交 {submitted_count}/{len(smiles_list)} 个化合物")
            else:
                logger.error(f"提交第 {i+1} 批失败")
                break

            # 延迟以避免API限制
            if i < total_batches - 1:
                logger.info(f"等待 {self.delay} 秒...")
                time.sleep(self.delay)

        return query_ids

    def _monitor_and_collect_results(self, query_ids: List[int], status: Dict) -> Dict[str, Dict]:
        """
        监控查询状态并收集结果

        Args:
            query_ids: 查询ID列表
            status: 状态字典

        Returns:
            分类结果字典，键为SMILES，值为分类信息
        """
        results = status.get('results', {})
        completed_queries = status.get('completed_queries', set())

        logger.info(f"开始监控 {len(query_ids)} 个查询的状态")

        while len(completed_queries) < len(query_ids):
            for query_id in query_ids:
                if query_id in completed_queries:
                    continue

                # 检查查询状态
                query_status = self.client.get_query_status(query_id)

                if not query_status:
                    logger.warning(f"无法获取查询 {query_id} 的状态")
                    continue

                status_value = query_status.get('classification_status')
                logger.info(f"查询 {query_id} 状态: {status_value}")

                if status_value == 'Done':
                    # 收集结果
                    query_results = self._collect_query_results(query_id)
                    if query_results:
                        results.update(query_results)
                        completed_queries.add(query_id)

                        # 保存状态
                        status['results'] = results
                        status['completed_queries'] = list(completed_queries)
                        self.save_status(status)

                        logger.info(f"查询 {query_id} 完成，收集到 {len(query_results)} 个结果")
                elif status_value in ['Failed', 'Timed out']:
                    logger.error(f"查询 {query_id} 失败: {status_value}")
                    completed_queries.add(query_id)

                    # 保存状态
                    status['completed_queries'] = list(completed_queries)
                    self.save_status(status)

            # 如果还有未完成的查询，等待一段时间再检查
            if len(completed_queries) < len(query_ids):
                logger.info(f"已完成 {len(completed_queries)}/{len(query_ids)} 个查询，等待60秒后继续检查...")
                time.sleep(60)

        logger.info(f"所有查询已完成，共收集到 {len(results)} 个分类结果")
        return results

    def _collect_query_results(self, query_id: int) -> Dict[str, Dict]:
        """
        收集单个查询的所有结果

        Args:
            query_id: 查询ID

        Returns:
            结果字典，键为SMILES，值为分类信息
        """
        results = {}
        page = 1

        while True:
            page_results = self.client.get_query_results(query_id, page)

            if not page_results:
                break

            entities = page_results.get('entities', [])

            if not entities:
                break

            for entity in entities:
                smiles = entity.get('smiles')
                if smiles:
                    results[smiles] = self._extract_classification_info(entity)

            # 检查是否还有更多页
            if len(entities) < 10:  # ClassyFire每页返回10个结果
                break

            page += 1

        return results

    def _extract_classification_info(self, entity: Dict) -> Dict:
        """
        从实体信息中提取分类信息

        Args:
            entity: ClassyFire返回的实体信息

        Returns:
            提取的分类信息字典
        """
        classification = {}

        # 基本信息
        classification['canonical_smiles'] = entity.get('smiles', '')
        classification['inchikey'] = entity.get('inchikey', '')
        classification['molecular_formula'] = entity.get('molecular_formula', '')

        # 分类层级信息
        for level in ['kingdom', 'superclass', 'class', 'subclass']:
            level_info = entity.get(level, {})
            if level_info:
                classification[f'{level}_name'] = level_info.get('name', '')
                classification[f'{level}_id'] = level_info.get('chemont_id', '')
            else:
                classification[f'{level}_name'] = ''
                classification[f'{level}_id'] = ''

        return classification

    def _save_results(self, df: pd.DataFrame, results: Dict[str, Dict], output_file: str):
        """
        保存分类结果到Excel文件

        Args:
            df: 原始DataFrame
            results: 分类结果字典
            output_file: 输出文件路径
        """
        # 创建结果DataFrame
        result_df = df.copy()

        # 添加分类结果列
        classification_columns = [
            'canonical_smiles', 'inchikey', 'molecular_formula',
            'kingdom_name', 'kingdom_id',
            'superclass_name', 'superclass_id',
            'class_name', 'class_id',
            'subclass_name', 'subclass_id'
        ]

        for col in classification_columns:
            result_df[col] = ''

        # 填充分类结果
        for idx, row in result_df.iterrows():
            smiles = row.get('smiles', '')
            if smiles in results:
                classification = results[smiles]
                for col in classification_columns:
                    result_df.at[idx, col] = classification.get(col, '')

        # 保存到Excel文件
        result_df.to_excel(output_file, index=False)
        logger.info(f"结果已保存到: {output_file}")

    def process_excel_file(self, input_file: str, output_file: str,
                          smiles_column: str = 'smiles') -> bool:
        """
        处理Excel文件中的SMILES进行分类

        Args:
            input_file: 输入Excel文件路径
            output_file: 输出Excel文件路径
            smiles_column: SMILES列名

        Returns:
            处理是否成功
        """
        try:
            # 读取输入文件
            logger.info(f"读取输入文件: {input_file}")
            df = pd.read_excel(input_file)

            if smiles_column not in df.columns:
                logger.error(f"未找到SMILES列: {smiles_column}")
                return False

            # 过滤有效的SMILES
            df = df.dropna(subset=[smiles_column])
            df = df[df[smiles_column].str.len() <= 2000]  # 过滤过长的SMILES

            logger.info(f"共有 {len(df)} 个有效的SMILES需要分类")

            # 加载之前的状态
            status = self.load_status()

            # 第一阶段：提交查询
            query_ids = self._submit_batches(df, smiles_column, status)

            if not query_ids:
                logger.error("没有成功提交的查询")
                return False

            # 第二阶段：监控和获取结果
            results = self._monitor_and_collect_results(query_ids, status)

            # 第三阶段：整合结果并保存
            self._save_results(df, results, output_file)

            logger.info(f"分类完成，结果已保存到: {output_file}")
            return True

        except Exception as e:
            logger.error(f"处理文件时发生错误: {e}")
            return False

def main():
    """主函数"""
    # 配置参数
    input_file = 'IECSC_WangHB.xlsx'      # 输入文件
    output_file = 'IECSC_WangHB_classyfire_results.xlsx'    # 输出文件
    smiles_column = 'smiles'              # SMILES列名
    batch_size = 50                       # 每批处理数量（建议不超过100）
    delay = 5.0                          # 提交间隔时间（秒）

    # 创建处理器
    processor = ClassyFireBatchProcessor(batch_size=batch_size, delay=delay)

    # 处理文件
    success = processor.process_excel_file(input_file, output_file, smiles_column)

    if success:
        logger.info("ClassyFire分类任务完成！")
    else:
        logger.error("ClassyFire分类任务失败！")

if __name__ == "__main__":
    main()