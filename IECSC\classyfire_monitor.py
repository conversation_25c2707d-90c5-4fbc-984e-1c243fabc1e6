#!/usr/bin/env python3
"""
ClassyFire监控脚本

用于监控已提交的ClassyFire查询状态并收集结果。
"""

import pickle
import time
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from classyfire_main import ClassyFireBatchProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def monitor_existing_queries():
    """监控现有查询的状态"""
    processor = ClassyFireBatchProcessor()
    status = processor.load_status()
    
    if not status:
        logger.error("未找到状态文件，请先运行主程序提交查询")
        return
    
    query_ids = status.get('query_ids', [])
    if not query_ids:
        logger.error("状态文件中没有查询ID")
        return
    
    logger.info(f"找到 {len(query_ids)} 个查询ID: {query_ids}")
    
    # 检查每个查询的状态
    for query_id in query_ids:
        query_status = processor.client.get_query_status(query_id)
        
        if query_status:
            status_value = query_status.get('classification_status', 'Unknown')
            num_entities = query_status.get('number_of_elements', 0)
            logger.info(f"查询 {query_id}: 状态={status_value}, 化合物数量={num_entities}")
        else:
            logger.warning(f"无法获取查询 {query_id} 的状态")

def collect_results_only():
    """仅收集已完成查询的结果"""
    processor = ClassyFireBatchProcessor()
    status = processor.load_status()
    
    if not status:
        logger.error("未找到状态文件")
        return
    
    query_ids = status.get('query_ids', [])
    if not query_ids:
        logger.error("状态文件中没有查询ID")
        return
    
    # 监控并收集结果
    results = processor._monitor_and_collect_results(query_ids, status)
    
    if results:
        logger.info(f"收集到 {len(results)} 个分类结果")
        
        # 如果有原始数据，保存完整结果
        try:
            import pandas as pd
            df = pd.read_excel('IECSC_WangHB.xlsx')
            processor._save_results(df, results, 'IECSC_WangHB_classyfire_results.xlsx')
        except Exception as e:
            logger.error(f"保存完整结果失败: {e}")
            
            # 保存简化结果
            import pandas as pd
            result_list = []
            for smiles, classification in results.items():
                row = {'smiles': smiles}
                row.update(classification)
                result_list.append(row)
            
            result_df = pd.DataFrame(result_list)
            result_df.to_excel('classyfire_results_only.xlsx', index=False)
            logger.info("已保存简化结果到 classyfire_results_only.xlsx")
    else:
        logger.warning("未收集到任何结果")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "monitor":
            monitor_existing_queries()
        elif command == "collect":
            collect_results_only()
        else:
            print("用法:")
            print("  python classyfire_monitor.py monitor   # 检查查询状态")
            print("  python classyfire_monitor.py collect   # 收集结果")
    else:
        print("ClassyFire监控脚本")
        print("用法:")
        print("  python classyfire_monitor.py monitor   # 检查查询状态")
        print("  python classyfire_monitor.py collect   # 收集结果")

if __name__ == "__main__":
    main()
