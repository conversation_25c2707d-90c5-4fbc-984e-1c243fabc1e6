FishLC50:{'max_depth': 19, 'min_samples_split': 6, 'min_samples_leaf': 2, 'max_features': 'sqrt', 'n_estimators': 150, 'bootstrap': False, 'max_samples': 1.0}
FishEL_NOEC:{'max_depth': 8, 'min_samples_split': 8, 'min_samples_leaf': 3, 'max_features': None, 'n_estimators': 450, 'bootstrap': False, 'max_samples': 0.8}
DMRepNOEC:{'max_depth': 9, 'min_samples_split': 7, 'min_samples_leaf': 1, 'max_features': None, 'n_estimators': 350, 'bootstrap': True, 'max_samples': 0.8}
DMImbEC50:{'max_depth': 18, 'min_samples_split': 10, 'min_samples_leaf': 4, 'max_features': 'log2', 'n_estimators': 150, 'bootstrap': False, 'max_samples': 0.8}
AlaGroErC50:{'max_depth': 15, 'min_samples_split': 9, 'min_samples_leaf': 3, 'max_features': 'log2', 'n_estimators': 150, 'bootstrap': True, 'max_samples': 0.7}
