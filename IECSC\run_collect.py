#!/usr/bin/env python3
"""
ClassyFire收集脚本 - 直接运行版

配置好参数后直接运行即可，无需命令行参数。
"""

# ==================== 配置区域 ====================
# 在这里修改您的配置参数

# 查询ID文件配置（提交脚本生成的文件）
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'      # 查询ID文件路径
# 或者使用JSON文件: 'IECSC_WangHB_query_info.json'

# 输出文件配置
OUTPUT_FILE = 'IECSC_WangHB_classified_results.xlsx'  # 输出Excel文件路径
ORIGINAL_FILE = 'IECSC_WangHB.xlsx'                   # 原始文件路径（用于合并数据）

# 收集参数配置
WAIT_FOR_COMPLETION = True                            # 是否等待所有查询完成
CHECK_INTERVAL = 60                                   # 检查间隔时间（秒）
CHECK_STATUS_ONLY = False                             # 设置为True仅检查状态，不收集结果

# ==================== 程序代码 ====================

import requests
import pandas as pd
import time
import json
import os
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire_collect.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireCollector:
    """ClassyFire结果收集器"""
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0'
        })
    
    def get_query_status(self, query_id: int) -> Optional[Dict]:
        """获取查询状态"""
        try:
            url = f"{self.base_url}/queries/{query_id}.json"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询 {query_id} 状态失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取查询 {query_id} 状态时发生错误: {e}")
            return None
    
    def get_query_results(self, query_id: int, page: int = 1) -> Optional[Dict]:
        """获取查询结果（分页）"""
        try:
            url = f"{self.base_url}/queries/{query_id}.json?page={page}"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询 {query_id} 结果失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取查询 {query_id} 结果时发生错误: {e}")
            return None
    
    def collect_query_results(self, query_id: int) -> Dict[str, Dict]:
        """收集单个查询的所有结果"""
        results = {}
        page = 1
        
        while True:
            page_results = self.get_query_results(query_id, page)
            
            if not page_results:
                break
            
            entities = page_results.get('entities', [])
            
            if not entities:
                break
            
            for entity in entities:
                smiles = entity.get('smiles')
                if smiles:
                    results[smiles] = self.extract_classification_info(entity)
            
            # 检查是否还有更多页
            if len(entities) < 10:  # ClassyFire每页返回10个结果
                break
            
            page += 1
        
        return results
    
    def extract_classification_info(self, entity: Dict) -> Dict:
        """从实体信息中提取分类信息"""
        classification = {}
        
        # 基本信息
        classification['canonical_smiles'] = entity.get('smiles', '')
        classification['inchikey'] = entity.get('inchikey', '')
        classification['molecular_formula'] = entity.get('molecular_formula', '')
        
        # 分类层级信息
        for level in ['kingdom', 'superclass', 'class', 'subclass']:
            level_info = entity.get(level, {})
            if level_info:
                classification[f'{level}_name'] = level_info.get('name', '')
                classification[f'{level}_id'] = level_info.get('chemont_id', '')
            else:
                classification[f'{level}_name'] = ''
                classification[f'{level}_id'] = ''
        
        return classification
    
    def check_query_status(self, query_ids: List[int]) -> Dict[int, Dict]:
        """检查多个查询的状态"""
        results = {}

        logger.info(f"检查 {len(query_ids)} 个查询的状态:")
        logger.info("=" * 80)

        for i, query_id in enumerate(query_ids, 1):
            query_status = self.get_query_status(query_id)

            if query_status:
                status_value = query_status.get('classification_status', 'Unknown')
                num_entities = query_status.get('number_of_elements', 0)
                label = query_status.get('label', '')

                results[query_id] = {
                    'status': status_value,
                    'num_entities': num_entities,
                    'label': label
                }

                # 状态显示
                status_icon = {
                    'Done': '✓',
                    'In Queue': '⏳',
                    'Processing': '🔄',
                    'Failed': '✗',
                    'Timed out': '⏰'
                }.get(status_value, '?')

                logger.info(f"{i:2d}. 查询 {query_id}: {status_icon} {status_value} ({num_entities} 个化合物)")
                if label:
                    logger.info(f"     标签: {label}")
            else:
                results[query_id] = {
                    'status': 'Error',
                    'num_entities': 0,
                    'label': ''
                }
                logger.info(f"{i:2d}. 查询 {query_id}: ✗ 无法获取状态")

        return results

    def show_status_summary(self, results: Dict[int, Dict]):
        """显示状态摘要"""
        logger.info("=" * 80)
        logger.info("状态摘要:")

        # 统计各种状态
        status_count = {}
        total_entities = 0

        for query_id, info in results.items():
            status = info['status']
            status_count[status] = status_count.get(status, 0) + 1
            total_entities += info['num_entities']

        # 显示统计
        for status, count in sorted(status_count.items()):
            icon = {
                'Done': '✓',
                'In Queue': '⏳',
                'Processing': '🔄',
                'Failed': '✗',
                'Timed out': '⏰',
                'Error': '❌'
            }.get(status, '?')

            logger.info(f"  {icon} {status}: {count} 个查询")

        logger.info(f"  📊 总化合物数: {total_entities}")

        # 计算完成率
        completed = status_count.get('Done', 0)
        total = len(results)
        completion_rate = (completed / total * 100) if total > 0 else 0

        logger.info(f"  📈 完成率: {completed}/{total} ({completion_rate:.1f}%)")

        # 给出建议
        logger.info("=" * 80)
        if completion_rate == 100:
            logger.info("🎉 所有查询已完成！")
        elif completion_rate > 0:
            logger.info("⏳ 部分查询已完成")
        else:
            logger.info("⏳ 查询仍在处理中，请稍后再检查")

        logger.info("=" * 80)
    
    def collect_all_results(self, query_ids: List[int], wait_for_completion: bool = True) -> Dict[str, Dict]:
        """收集所有查询的结果"""
        all_results = {}
        completed_queries = set()
        
        logger.info(f"开始收集 {len(query_ids)} 个查询的结果")
        
        while len(completed_queries) < len(query_ids):
            for query_id in query_ids:
                if query_id in completed_queries:
                    continue
                
                # 检查查询状态
                query_status = self.get_query_status(query_id)
                
                if not query_status:
                    logger.warning(f"跳过查询 {query_id}（无法获取状态）")
                    completed_queries.add(query_id)
                    continue
                
                status_value = query_status.get('classification_status')
                logger.info(f"查询 {query_id} 状态: {status_value}")
                
                if status_value == 'Done':
                    # 收集结果
                    query_results = self.collect_query_results(query_id)
                    if query_results:
                        all_results.update(query_results)
                        completed_queries.add(query_id)
                        logger.info(f"✓ 查询 {query_id} 完成，收集到 {len(query_results)} 个结果")
                    else:
                        logger.warning(f"查询 {query_id} 完成但无结果")
                        completed_queries.add(query_id)
                        
                elif status_value in ['Failed', 'Timed out']:
                    logger.error(f"✗ 查询 {query_id} 失败: {status_value}")
                    completed_queries.add(query_id)
                elif not wait_for_completion:
                    logger.info(f"查询 {query_id} 尚未完成，跳过")
                    completed_queries.add(query_id)
            
            # 如果还有未完成的查询且需要等待，则等待一段时间再检查
            if wait_for_completion and len(completed_queries) < len(query_ids):
                remaining = len(query_ids) - len(completed_queries)
                logger.info(f"还有 {remaining} 个查询未完成，等待{CHECK_INTERVAL}秒后继续检查...")
                time.sleep(CHECK_INTERVAL)
        
        logger.info(f"收集完成，共收集到 {len(all_results)} 个分类结果")
        return all_results
    
    def save_results(self, results: Dict[str, Dict], output_file: str, original_file: str = None):
        """保存结果到Excel文件"""
        if original_file and os.path.exists(original_file):
            # 如果有原始文件，合并结果
            logger.info(f"合并原始数据: {original_file}")
            df = pd.read_excel(original_file)
            
            # 添加分类结果列
            classification_columns = [
                'canonical_smiles', 'inchikey', 'molecular_formula',
                'kingdom_name', 'kingdom_id',
                'superclass_name', 'superclass_id',
                'class_name', 'class_id',
                'subclass_name', 'subclass_id'
            ]
            
            for col in classification_columns:
                df[col] = ''
            
            # 填充分类结果
            smiles_column = None
            for col in df.columns:
                if 'smiles' in col.lower():
                    smiles_column = col
                    break
            
            if smiles_column:
                matched_count = 0
                for idx, row in df.iterrows():
                    smiles = row.get(smiles_column, '')
                    if smiles in results:
                        classification = results[smiles]
                        for col in classification_columns:
                            df.at[idx, col] = classification.get(col, '')
                        matched_count += 1
                
                logger.info(f"成功匹配 {matched_count}/{len(df)} 个化合物的分类结果")
            else:
                logger.warning("未找到SMILES列，无法匹配结果")
            
            df.to_excel(output_file, index=False)
        else:
            # 仅保存分类结果
            logger.info("保存分类结果（不包含原始数据）")
            result_list = []
            for smiles, classification in results.items():
                row = {'smiles': smiles}
                row.update(classification)
                result_list.append(row)
            
            result_df = pd.DataFrame(result_list)
            result_df.to_excel(output_file, index=False)
        
        logger.info(f"结果已保存到: {output_file}")

def load_query_ids(query_file: str) -> List[int]:
    """从文件加载查询ID"""
    if not os.path.exists(query_file):
        logger.error(f"查询ID文件不存在: {query_file}")
        return []
    
    query_ids = []
    
    if query_file.endswith('.json'):
        # JSON格式
        try:
            with open(query_file, 'r', encoding='utf-8') as f:
                query_info = json.load(f)
                query_ids = query_info.get('query_ids', [])
                logger.info(f"从JSON文件加载了 {len(query_ids)} 个查询ID")
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
    else:
        # 文本格式
        try:
            with open(query_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and line.isdigit():
                        query_ids.append(int(line))
            logger.info(f"从文本文件加载了 {len(query_ids)} 个查询ID")
        except Exception as e:
            logger.error(f"读取文本文件失败: {e}")
    
    return query_ids

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("ClassyFire化学品分类 - 收集脚本")
    logger.info("=" * 60)
    
    # 显示配置信息
    logger.info("当前配置:")
    logger.info(f"  查询ID文件: {QUERY_IDS_FILE}")
    logger.info(f"  输出文件: {OUTPUT_FILE}")
    logger.info(f"  原始文件: {ORIGINAL_FILE}")
    logger.info(f"  等待完成: {WAIT_FOR_COMPLETION}")
    logger.info(f"  检查间隔: {CHECK_INTERVAL}秒")
    logger.info("-" * 60)
    
    # 加载查询ID
    query_ids = load_query_ids(QUERY_IDS_FILE)
    
    if not query_ids:
        logger.error("未找到有效的查询ID，请先运行 run_submit.py")
        return
    
    # 创建收集器
    collector = ClassyFireCollector()
    
    # 首先检查状态
    logger.info("检查查询状态...")
    status_results = collector.check_query_status(query_ids)
    collector.show_status_summary(status_results)

    # 如果只是检查状态，则结束
    if CHECK_STATUS_ONLY:
        logger.info("仅检查状态模式，程序结束")
        return

    # 收集结果
    logger.info("开始收集结果...")
    results = collector.collect_all_results(query_ids, WAIT_FOR_COMPLETION)
    
    # 保存结果
    if results:
        logger.info("-" * 60)
        collector.save_results(results, OUTPUT_FILE, ORIGINAL_FILE)
        logger.info("=" * 60)
        logger.info(f"✓ 收集完成！共收集到 {len(results)} 个分类结果")
        logger.info(f"结果文件: {OUTPUT_FILE}")
    else:
        logger.warning("未收集到任何结果")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
