FishLC50:{'max_depth': 16, 'min_samples_split': 2, 'min_samples_leaf': 1, 'max_features': 'sqrt', 'n_estimators': 400, 'bootstrap': False, 'max_samples': 0.9}
FishEL_NOEC:{'max_depth': 12, 'min_samples_split': 10, 'min_samples_leaf': 3, 'max_features': 'sqrt', 'n_estimators': 100, 'bootstrap': False, 'max_samples': 0.9}
DMRepNOEC:{'max_depth': 17, 'min_samples_split': 4, 'min_samples_leaf': 3, 'max_features': 'sqrt', 'n_estimators': 100, 'bootstrap': False, 'max_samples': 0.7}
DMImbEC50:{'max_depth': 15, 'min_samples_split': 2, 'min_samples_leaf': 4, 'max_features': 'log2', 'n_estimators': 150, 'bootstrap': True, 'max_samples': 0.7}
AlaGroErC50:{'max_depth': 11, 'min_samples_split': 6, 'min_samples_leaf': 4, 'max_features': 'log2', 'n_estimators': 200, 'bootstrap': False, 'max_samples': 0.9}
