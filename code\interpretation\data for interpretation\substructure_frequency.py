import pandas as pd
from rdkit import Chem
from collections import Counter
from datetime import datetime

# --- 1. 定义子结构，包含英文名、SMARTS 和中文名 ---
# 修改了数据结构：值现在是一个元组 (SMARTS模式, 中文名称)
SUBSTRUCTURE_DEFINITIONS = {
    # =======================================================
    # 主要官能团 (Functional Groups)
    # =======================================================
    'Alcohol':         ('[#6][OH1]', '醇羟基'),
    'Phenol':          ('[c][OH1]', '酚羟基'),
    'Carboxylic Acid': ('[CX3](=O)[OX2H1]', '羧基'),
    'Ester':           ('[#6][CX3](=O)[OX1][#6]', '酯基'),
    'Ether':           ('[OD2]([#6])[#6]', '醚键'),
    'Amine':           ('[NX3;H2,H1,H0;!$(N=O)]', '氨基'),
    'Amide':           ('[C](=[O])[N]', '酰胺基'),
    'Ketone':          ('[#6][C](=[O])[#6]', '酮羰基'),
    'Aldehyde':        ('[CX3H1](=O)', '醛基'),
    'Nitro Compound':  ('[$([NX3](=O)=O),$([NX3+](=O)[O-])]', '硝基'),
    'Thiol':           ('[#6][SH]', '硫醇基'),
    'Sulfonamide':     ('[S](=[O])(=[O])N', '磺酰胺基'),
    
    # =======================================================
    # 结构骨架或特征 (Structural Scaffolds / Features)
    # =======================================================
    'Aromatic Ring':   ('a', '芳香环'),
    'Pyridine Ring':   ('c1ncccc1', '吡啶环'),
    'Indole Ring':     ('c1c2c(cn1)cccc2', '吲哚环'),
    'Benzene Ring':    ('c1ccccc1', '苯环'),
    
    # =======================================================
    # 广义化学类别 (Broad Chemical Classes)
    # =======================================================
    'Halogen Compound': ('[F,Cl,Br,I]', '卤代物'),
}

# 预编译 SMARTS 模式以提高效率
# 修改了这里的逻辑，从元组中提取 SMARTS (value[0])
COMPILED_PATTERNS = {name: Chem.MolFromSmarts(value[0]) for name, value in SUBSTRUCTURE_DEFINITIONS.items()}


def analyze_substructure_frequency(smiles_list: list) -> tuple:
    """
    分析SMILES列表中每个预定义子结构的出现频率。
    (此函数无需修改)
    """
    counts = {name: 0 for name in SUBSTRUCTURE_DEFINITIONS.keys()}
    valid_mol_count = 0
    invalid_smiles_count = 0

    print(f"开始分析 {len(smiles_list)} 个SMILES...")

    for i, smiles in enumerate(smiles_list):
        if (i + 1) % 100 == 0:
            print(f"  已处理 {i + 1}/{len(smiles_list)}...")

        if not isinstance(smiles, str) or smiles.strip() == '':
            invalid_smiles_count += 1
            continue

        mol = Chem.MolFromSmiles(smiles)

        if mol is None:
            invalid_smiles_count += 1
            continue

        valid_mol_count += 1

        for name, pattern in COMPILED_PATTERNS.items():
            if mol.HasSubstructMatch(pattern):
                counts[name] += 1

    print("分析完成。")
    return counts, valid_mol_count, invalid_smiles_count


def identify_compound_categories(smiles: str) -> list:
    """
    识别单个化合物的所属子结构类别。

    Args:
        smiles: 化合物的SMILES字符串

    Returns:
        list: 包含该化合物所有匹配子结构类别的列表
    """
    if not isinstance(smiles, str) or smiles.strip() == '':
        return []

    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return []

    matched_categories = []
    for name, pattern in COMPILED_PATTERNS.items():
        if mol.HasSubstructMatch(pattern):
            matched_categories.append(name)

    return matched_categories


def process_compounds_with_categories(input_file: str, output_file: str, smiles_column: str = 'smiles'):
    """
    处理化合物文件，为每个化合物添加子结构类别标记。

    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        smiles_column: SMILES列的名称
    """
    print(f"正在读取文件: {input_file}")

    try:
        df = pd.read_csv(input_file)
    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_file}' 未找到。")
        return
    except Exception as e:
        print(f"错误: 读取文件时出现问题: {e}")
        return

    if smiles_column not in df.columns:
        print(f"错误: 在文件中找不到名为 '{smiles_column}' 的列。")
        print(f"可用列: {list(df.columns)}")
        return

    print(f"开始处理 {len(df)} 个化合物...")

    # 为每个化合物识别子结构类别
    categories_list = []
    for i, smiles in enumerate(df[smiles_column]):
        if (i + 1) % 100 == 0:
            print(f"  已处理 {i + 1}/{len(df)}...")

        categories = identify_compound_categories(smiles)
        # 将类别列表转换为字符串，用分号分隔
        categories_str = '; '.join(categories) if categories else 'No Match'
        categories_list.append(categories_str)

    # 添加新的'set'列
    df['set'] = categories_list

    # 保存结果
    print(f"正在保存结果到: {output_file}")
    df.to_csv(output_file, index=False)
    print("处理完成！")

    # 打印统计信息
    print("\n=== 统计信息 ===")
    print(f"总化合物数: {len(df)}")

    # 统计各类别的出现频率
    all_categories = []
    for cat_str in categories_list:
        if cat_str != 'No Match':
            all_categories.extend(cat_str.split('; '))

    if all_categories:
        category_counts = Counter(all_categories)
        print("\n各子结构类别出现频率:")
        for category, count in category_counts.most_common():
            percentage = (count / len(df)) * 100
            print(f"  {category}: {count} ({percentage:.1f}%)")

    no_match_count = categories_list.count('No Match')
    print(f"\n未匹配任何子结构的化合物: {no_match_count} ({(no_match_count/len(df)*100):.1f}%)")


def write_report_to_txt(results: dict, total_valid: int, total_invalid: int, input_file: str, output_file: str):
    """
    将分析结果写入一个格式化的文本文件。
    """
    print(f"正在将报告写入 {output_file}...")
    
    sorted_results = dict(sorted(results.items(), key=lambda item: item[1], reverse=True))
    
    # --- 修改点: 重新计算列的最大宽度 ---
    # 我们需要构建包含中英文的完整显示名称，然后计算其最大长度以用于对齐
    display_names = {name: f"{name} ({value[1]})" for name, value in SUBSTRUCTURE_DEFINITIONS.items()}
    max_len = max(len(dn) for dn in display_names.values()) + 2

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("--- Substructure Frequency Analysis Report ---\n\n")
        f.write(f"Timestamp:          {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Source Excel File:  {input_file}\n")
        f.write("----------------------------------------------\n")
        f.write(f"Total SMILES Processed: {total_valid + total_invalid}\n")
        f.write(f"Valid Molecules:        {total_valid}\n")
        f.write(f"Invalid/Empty SMILES:   {total_invalid}\n")
        f.write("----------------------------------------------\n\n")
        
        f.write("Frequency of Substructures (counting molecules that contain the structure):\n\n")
        
        header = f"{'Substructure'.ljust(max_len)} {'Count'.rjust(10)} {'Frequency'.rjust(12)}\n"
        f.write(header)
        f.write(f"{'-' * max_len} {'-' * 10} {'-' * 12}\n")

        if total_valid > 0:
            for name, count in sorted_results.items():
                percentage = (count / total_valid) * 100
                
                # --- 修改点: 构建并使用新的显示名称 ---
                display_name = display_names[name]
                line = f"{display_name.ljust(max_len)} {str(count).rjust(10)} {f'{percentage:11.2f}%'.rjust(12)}\n"
                f.write(line)
        else:
            f.write("No valid molecules found to analyze.\n")
            
    print("报告已成功保存！")


# --- 用户配置区域 (根据您的文件名修改) ---
if __name__ == "__main__":
    # 1. 设置输入文件名
    INPUT_FILE = 'FishAT_test_for_interpretation.csv'

    # 2. 指定包含SMILES的列的名称
    SMILES_COLUMN_NAME = 'smiles'

    # 3. 设置输出文件名（在原文件基础上添加set列）
    OUTPUT_FILE = 'FishAT_test_for_interpretation_with_categories.csv'

    # --- 执行脚本 ---
    print("=== 化合物子结构类别识别程序 ===")
    print(f"输入文件: {INPUT_FILE}")
    print(f"输出文件: {OUTPUT_FILE}")
    print(f"SMILES列名: {SMILES_COLUMN_NAME}")
    print()

    # 处理化合物并添加类别标记
    process_compounds_with_categories(INPUT_FILE, OUTPUT_FILE, SMILES_COLUMN_NAME)