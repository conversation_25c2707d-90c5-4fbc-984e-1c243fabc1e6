{"cells": [{"cell_type": "code", "execution_count": 8, "id": "2270f0d1", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [81.0, 100 - 81.0]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'FishAT'\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($\\mathit{\\mathbf{F}}_{\\mathbf{sub-1}}$)',\n", "    r'Ether ($\\mathit{\\mathbf{F}}_{\\mathbf{sub-2}}$)',\n", "    r'<PERSON><PERSON> ($\\mathit{\\mathbf{F}}_{\\mathbf{sub-3}}$)',\n", "    r'Amine ($\\mathit{\\mathbf{F}}_{\\mathbf{sub-4}}$)',\n", "    # r'Ether ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [62.7, 34.1, 34.0, 33.1, ]\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#377eb8'  # 深青色\n", "color_light_blue = '#a6cee3' # 浅青色\n", "# color_dark_blue = '#01665e'  # 深青色\n", "# color_light_blue = '#c7eae5' # 浅青色\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "plt.rcParams['font.weight'] = 'bold'  # 设置全局字体加粗\n", "# 设置数学字体也使用Times New Roman并加粗\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman:bold'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic:bold'\n", "plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "# 在圆环中心添加文字（加粗）\n", "ax1.text(0, 0, center_text, ha='center', va='center', \n", "         fontsize=font_center_text_size, fontname=font_name, fontweight='bold')\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "# 创建加粗的图例\n", "legend_font = FontProperties(family=font_name, size=font_main_label_size, weight='bold')\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=legend_font)\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "# 设置 y 轴标签的字体大小和加粗\n", "for tick in ax2.get_yticklabels():\n", "    tick.set_fontsize(font_main_label_size)\n", "    tick.set_fontweight('bold')\n", "ax2.tick_params(axis='y', length=0) # length=0 隐藏刻度线\n", "# 在每个条形图的右侧添加数值标签（加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name,\n", "             fontweight='bold')    # 加粗\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 2, "id": "edafa6f1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [72.8, 100 - 72.8]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'FishCT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Halogen ($F_{\\mathrm{sub-2}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-3}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-4}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [75.4, 43.2, 30.2, 27.4, 26.8]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#6a51a3'  # 深青色\n", "color_light_blue = '#bcbddc' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 14  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 12       # 如 63.7%\n", "font_center_text_size = 20 # 圆环中心 F_BA 的大小\n", "font_legend_size = 12      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 2, "id": "03f42300", "metadata": {}, "outputs": [{"data": {"image/png": "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***********************************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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [60.2, 100 - 60.2]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'DMCT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-2}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-3}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [62.7, 33.8, 29.6, 28.5]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#d95f0e'  # 深青色\n", "color_light_blue = '#fdd0a2' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "fdf904d6", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [87.9, 100 - 87.9]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'DMAT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-2}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-3}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [57.8, 35.2, 34.4, 25.6]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#238b45'  # 深青色\n", "color_light_blue = '#a1d99b' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 14  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 12       # 如 63.7%\n", "font_center_text_size = 20 # 圆环中心 F_BA 的大小\n", "font_legend_size = 12      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "id": "68a067d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [89.9, 100 - 89.9]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'AlgAT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-2}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-3}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [57.3, 37.2, 32.5, 29.5]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#01665e'  # 深青色\n", "color_light_blue = '#c7eae5' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "2777b0b4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56a357bb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}