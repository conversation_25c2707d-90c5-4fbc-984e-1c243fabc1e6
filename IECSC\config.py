#!/usr/bin/env python3
"""
ClassyFire配置文件

包含所有可配置的参数和设置。
"""

# 文件路径配置
INPUT_FILE = 'IECSC_WangHB.xlsx'                    # 输入Excel文件
OUTPUT_FILE = 'IECSC_WangHB_classyfire_results.xlsx'  # 输出Excel文件
SMILES_COLUMN = 'smiles'                             # SMILES列名

# API配置
CLASSYFIRE_BASE_URL = "http://classyfire.wishartlab.com"
REQUEST_TIMEOUT = 30                                 # 请求超时时间（秒）
USER_AGENT = 'ClassyFire-Python-Client/1.0'

# 批处理配置
BATCH_SIZE = 50                                      # 每批处理的化合物数量（建议不超过100）
SUBMIT_DELAY = 5.0                                   # 提交间隔时间（秒）
MONITOR_INTERVAL = 60                                # 监控间隔时间（秒）
MAX_SMILES_LENGTH = 2000                             # 最大SMILES长度

# 状态文件
STATUS_FILE = "classyfire_status.pkl"               # 状态保存文件

# 日志配置
LOG_FILE = 'classyfire.log'                         # 日志文件
LOG_LEVEL = 'INFO'                                   # 日志级别
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# 输出列配置
OUTPUT_COLUMNS = [
    'canonical_smiles',
    'inchikey', 
    'molecular_formula',
    'kingdom_name',
    'kingdom_id',
    'superclass_name',
    'superclass_id',
    'class_name',
    'class_id',
    'subclass_name',
    'subclass_id'
]

# ClassyFire分类层级
CLASSIFICATION_LEVELS = ['kingdom', 'superclass', 'class', 'subclass']
