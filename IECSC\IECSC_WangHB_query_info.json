{"input_file": "IECSC_WangHB.xlsx", "smiles_column": "smiles", "total_compounds": 18905, "batch_size": 50, "query_ids": [12606841, 12606842, 12606843, 12606844, 12606845, 12606846, 12606847, 12606848, 12606849, 12606850, 12606851, 12606852, 12606853, 12606854, 12606855, 12606856, 12606857, 12606858, 12606859, 12606860, 12606861, 12606862, 12606863, 12606864, 12606865, 12606866, 12606867, 12606868, 12606869, 12606870, 12606871, 12606872, 12606873, 12606874, 12606875, 12606876, 12606877, 12606878, 12606879, 12606880, 12606881, 12606882, 12606883, 12606884, 12606885, 12606886, 12606887, 12606888, 12606889, 12606890, 12606891, 12606892, 12606893, 12606894, 12606895, 12606896, 12606897, 12606898, 12606899, 12606900, 12606901, 12606902, 12606903, 12606904, 12606905, 12606906, 12606907, 12606908, 12606909, 12606910, 12606911, 12606912, 12606913, 12606914, 12606915, 12606916, 12606917, 12606918, 12606919, 12606920, 12606921, 12606922, 12606923, 12606924, 12606925, 12606926, 12606927, 12606928, 12606929, 12606930, 12606931, 12606932, 12606933, 12606934, 12606935, 12606936, 12606937, 12606938, 12606939, 12606940, 12606941, 12606942, 12606943, 12606944, 12606945, 12606946, 12606947, 12606948, 12606949, 12606950, 12606951, 12606952, 12606953, 12606954, 12606955, 12606956, 12606957, 12606958, 12606959, 12606960, 12606961, 12606962, 12606963, 12606964, 12606965, 12606966, 12606967, 12606968, 12606969, 12606970, 12606971, 12606972, 12606973, 12606974, 12606975, 12606976, 12606977, 12606978, 12606979, 12606980, 12606981, 12606982, 12606983, 12606984, 12606985, 12606986, 12606987, 12606988, 12606989, 12606990, 12606991, 12606992, 12606993, 12606994, 12606995, 12606996, 12606997, 12606998, 12606999, 12607000, 12607001, 12607002, 12607003, 12607004, 12607005, 12607006, 12607007, 12607008, 12607009, 12607010, 12607011, 12607012, 12607013, 12607014, 12607015, 12607016, 12607017, 12607018, 12607019, 12607020, 12607021, 12607022, 12607023, 12607024, 12607025, 12607026, 12607027, 12607028, 12607029, 12607030, 12607031, 12607032, 12607033, 12607034, 12607035, 12607036, 12607037, 12607038, 12607039, 12607040, 12607041, 12607042, 12607043, 12607044, 12607045, 12607046, 12607047, 12607048, 12607049, 12607050, 12607051, 12607052, 12607053, 12607054, 12607055, 12607056, 12607057, 12607058, 12607059, 12607060, 12607061, 12607062, 12607063, 12607064, 12607065, 12607066, 12607067, 12607068, 12607069, 12607070, 12607071, 12607072, 12607073, 12607074, 12607075, 12607076, 12607077, 12607078, 12607079, 12607080, 12607081, 12607082, 12607083, 12607084, 12607085, 12607086, 12607087, 12607088, 12607089, 12607090, 12607091, 12607092, 12607093, 12607094, 12607095, 12607096, 12607097, 12607098, 12607099, 12607100, 12607101, 12607102, 12607103, 12607104, 12607105, 12607106, 12607107, 12607108, 12607109, 12607110, 12607111, 12607112, 12607113, 12607114, 12607115, 12607116, 12607117, 12607118, 12607119, 12607120, 12607121, 12607122, 12607123, 12607124, 12607125, 12607126, 12607127, 12607128, 12607129, 12607130, 12607131, 12607132, 12607133, 12607134, 12607135, 12607136, 12607137, 12607138, 12607139, 12607140, 12607141, 12607142, 12607143, 12607144, 12607145, 12607146, 12607147, 12607148, 12607149, 12607150, 12607151, 12607152, 12607153, 12607154, 12607155, 12607156, 12607157, 12607158, 12607159, 12607160, 12607161, 12607162, 12607163, 12607164, 12607165, 12607166, 12607167, 12607168, 12607169, 12607170, 12607171, 12607172, 12607173, 12607174, 12607175, 12607176, 12607177, 12607178, 12607179, 12607180, 12607181, 12607182, 12607183, 12607184, 12607185, 12607186, 12607187, 12607188, 12607189, 12607190, 12607191, 12607192, 12607193, 12607194, 12607195, 12607196, 12607197, 12607198, 12607199, 12607200, 12607201, 12607202, 12607203, 12607204, 12607205, 12607206, 12607207, 12607208, 12607209, 12607210, 12607211, 12607212, 12607213, 12607214, 12607215, 12607216, 12607217, 12607218, 12607219], "submit_time": 1756266009.0672417, "submit_time_readable": "2025-08-27 11:40:09"}