#!/usr/bin/env python3
"""
ClassyFire收集脚本

专门用于监控查询状态并收集ClassyFire分类结果。
"""

import requests
import pandas as pd
import time
import json
import os
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire_collect.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireCollector:
    """ClassyFire结果收集器"""
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0'
        })
    
    def get_query_status(self, query_id: int) -> Optional[Dict]:
        """获取查询状态"""
        try:
            url = f"{self.base_url}/queries/{query_id}.json"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询 {query_id} 状态失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取查询 {query_id} 状态时发生错误: {e}")
            return None
    
    def get_query_results(self, query_id: int, page: int = 1) -> Optional[Dict]:
        """获取查询结果（分页）"""
        try:
            url = f"{self.base_url}/queries/{query_id}.json?page={page}"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取查询 {query_id} 结果失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取查询 {query_id} 结果时发生错误: {e}")
            return None
    
    def collect_query_results(self, query_id: int) -> Dict[str, Dict]:
        """收集单个查询的所有结果"""
        results = {}
        page = 1
        
        while True:
            page_results = self.get_query_results(query_id, page)
            
            if not page_results:
                break
            
            entities = page_results.get('entities', [])
            
            if not entities:
                break
            
            for entity in entities:
                smiles = entity.get('smiles')
                if smiles:
                    results[smiles] = self.extract_classification_info(entity)
            
            # 检查是否还有更多页
            if len(entities) < 10:  # ClassyFire每页返回10个结果
                break
            
            page += 1
        
        return results
    
    def extract_classification_info(self, entity: Dict) -> Dict:
        """从实体信息中提取分类信息"""
        classification = {}
        
        # 基本信息
        classification['canonical_smiles'] = entity.get('smiles', '')
        classification['inchikey'] = entity.get('inchikey', '')
        classification['molecular_formula'] = entity.get('molecular_formula', '')
        
        # 分类层级信息
        for level in ['kingdom', 'superclass', 'class', 'subclass']:
            level_info = entity.get(level, {})
            if level_info:
                classification[f'{level}_name'] = level_info.get('name', '')
                classification[f'{level}_id'] = level_info.get('chemont_id', '')
            else:
                classification[f'{level}_name'] = ''
                classification[f'{level}_id'] = ''
        
        return classification
    
    def check_query_status(self, query_ids: List[int]) -> Dict[int, str]:
        """检查多个查询的状态"""
        status_dict = {}
        
        for query_id in query_ids:
            query_status = self.get_query_status(query_id)
            
            if query_status:
                status_value = query_status.get('classification_status', 'Unknown')
                num_entities = query_status.get('number_of_elements', 0)
                status_dict[query_id] = status_value
                logger.info(f"查询 {query_id}: 状态={status_value}, 化合物数量={num_entities}")
            else:
                status_dict[query_id] = 'Error'
                logger.warning(f"无法获取查询 {query_id} 的状态")
        
        return status_dict
    
    def collect_all_results(self, query_ids: List[int], wait_for_completion: bool = True) -> Dict[str, Dict]:
        """收集所有查询的结果"""
        all_results = {}
        completed_queries = set()
        
        while len(completed_queries) < len(query_ids):
            for query_id in query_ids:
                if query_id in completed_queries:
                    continue
                
                # 检查查询状态
                query_status = self.get_query_status(query_id)
                
                if not query_status:
                    logger.warning(f"跳过查询 {query_id}（无法获取状态）")
                    completed_queries.add(query_id)
                    continue
                
                status_value = query_status.get('classification_status')
                logger.info(f"查询 {query_id} 状态: {status_value}")
                
                if status_value == 'Done':
                    # 收集结果
                    query_results = self.collect_query_results(query_id)
                    if query_results:
                        all_results.update(query_results)
                        completed_queries.add(query_id)
                        logger.info(f"查询 {query_id} 完成，收集到 {len(query_results)} 个结果")
                    else:
                        logger.warning(f"查询 {query_id} 完成但无结果")
                        completed_queries.add(query_id)
                        
                elif status_value in ['Failed', 'Timed out']:
                    logger.error(f"查询 {query_id} 失败: {status_value}")
                    completed_queries.add(query_id)
                elif not wait_for_completion:
                    logger.info(f"查询 {query_id} 尚未完成，跳过")
                    completed_queries.add(query_id)
            
            # 如果还有未完成的查询且需要等待，则等待一段时间再检查
            if wait_for_completion and len(completed_queries) < len(query_ids):
                remaining = len(query_ids) - len(completed_queries)
                logger.info(f"还有 {remaining} 个查询未完成，等待60秒后继续检查...")
                time.sleep(60)
        
        logger.info(f"收集完成，共收集到 {len(all_results)} 个分类结果")
        return all_results
    
    def save_results(self, results: Dict[str, Dict], output_file: str, original_file: str = None):
        """保存结果到Excel文件"""
        if original_file and os.path.exists(original_file):
            # 如果有原始文件，合并结果
            df = pd.read_excel(original_file)
            
            # 添加分类结果列
            classification_columns = [
                'canonical_smiles', 'inchikey', 'molecular_formula',
                'kingdom_name', 'kingdom_id',
                'superclass_name', 'superclass_id',
                'class_name', 'class_id',
                'subclass_name', 'subclass_id'
            ]
            
            for col in classification_columns:
                df[col] = ''
            
            # 填充分类结果
            smiles_column = 'smiles'  # 假设SMILES列名为'smiles'
            for idx, row in df.iterrows():
                smiles = row.get(smiles_column, '')
                if smiles in results:
                    classification = results[smiles]
                    for col in classification_columns:
                        df.at[idx, col] = classification.get(col, '')
            
            df.to_excel(output_file, index=False)
        else:
            # 仅保存分类结果
            result_list = []
            for smiles, classification in results.items():
                row = {'smiles': smiles}
                row.update(classification)
                result_list.append(row)
            
            result_df = pd.DataFrame(result_list)
            result_df.to_excel(output_file, index=False)
        
        logger.info(f"结果已保存到: {output_file}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python classyfire_collect.py <query_ids_file> [output_file] [original_file]")
        print("  python classyfire_collect.py status <query_ids_file>")
        print("")
        print("示例:")
        print("  python classyfire_collect.py IECSC_WangHB_query_ids.txt")
        print("  python classyfire_collect.py IECSC_WangHB_query_ids.txt results.xlsx IECSC_WangHB.xlsx")
        print("  python classyfire_collect.py status IECSC_WangHB_query_ids.txt")
        return
    
    collector = ClassyFireCollector()
    
    if sys.argv[1] == "status":
        # 仅检查状态
        if len(sys.argv) < 3:
            logger.error("请提供查询ID文件")
            return
            
        query_file = sys.argv[2]
        
        if not os.path.exists(query_file):
            logger.error(f"查询ID文件不存在: {query_file}")
            return
        
        # 读取查询ID
        query_ids = []
        with open(query_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and line.isdigit():
                    query_ids.append(int(line))
        
        if not query_ids:
            logger.error("未找到有效的查询ID")
            return
        
        logger.info(f"检查 {len(query_ids)} 个查询的状态")
        status_dict = collector.check_query_status(query_ids)
        
        # 统计状态
        status_count = {}
        for status in status_dict.values():
            status_count[status] = status_count.get(status, 0) + 1
        
        logger.info("状态统计:")
        for status, count in status_count.items():
            logger.info(f"  {status}: {count}")
    
    else:
        # 收集结果
        query_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else f"{os.path.splitext(query_file)[0]}_results.xlsx"
        original_file = sys.argv[3] if len(sys.argv) > 3 else None
        
        if not os.path.exists(query_file):
            logger.error(f"查询ID文件不存在: {query_file}")
            return
        
        # 读取查询ID
        query_ids = []
        if query_file.endswith('.json'):
            # JSON格式
            with open(query_file, 'r') as f:
                query_info = json.load(f)
                query_ids = query_info.get('query_ids', [])
                original_file = original_file or query_info.get('input_file')
        else:
            # 文本格式
            with open(query_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and line.isdigit():
                        query_ids.append(int(line))
        
        if not query_ids:
            logger.error("未找到有效的查询ID")
            return
        
        logger.info(f"开始收集 {len(query_ids)} 个查询的结果")
        logger.info(f"输出文件: {output_file}")
        if original_file:
            logger.info(f"原始文件: {original_file}")
        
        # 收集结果
        results = collector.collect_all_results(query_ids, wait_for_completion=True)
        
        if results:
            collector.save_results(results, output_file, original_file)
            logger.info("收集完成！")
        else:
            logger.warning("未收集到任何结果")

if __name__ == "__main__":
    main()
