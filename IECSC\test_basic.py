#!/usr/bin/env python3
"""
基本功能测试脚本

测试ClassyFire工具的基本功能是否正常。
"""

import pandas as pd
import os
import sys
import requests
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dependencies():
    """测试依赖包"""
    logger.info("=== 测试依赖包 ===")
    
    try:
        import requests
        import pandas as pd
        logger.info("✓ 依赖包导入成功")
        return True
    except ImportError as e:
        logger.error(f"✗ 依赖包导入失败: {e}")
        logger.error("请运行: pip install -r requirements.txt")
        return False

def test_network():
    """测试网络连接"""
    logger.info("=== 测试网络连接 ===")
    
    try:
        response = requests.get("http://classyfire.wishartlab.com", timeout=10)
        if response.status_code == 200:
            logger.info("✓ ClassyFire网站连接正常")
            return True
        else:
            logger.warning(f"⚠ ClassyFire网站响应异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"✗ 网络连接失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    logger.info("=== 测试文件操作 ===")
    
    try:
        # 创建测试数据
        test_data = {
            'smiles': ['CCO', 'c1ccccc1', 'CC(=O)O'],
            'name': ['Ethanol', 'Benzene', 'Acetic acid']
        }
        
        df = pd.DataFrame(test_data)
        test_file = 'test_temp.xlsx'
        
        # 测试写入
        df.to_excel(test_file, index=False)
        
        # 测试读取
        df_read = pd.read_excel(test_file)
        
        if 'smiles' in df_read.columns and len(df_read) == 3:
            logger.info("✓ Excel文件读写功能正常")
            success = True
        else:
            logger.error("✗ Excel文件读写功能异常")
            success = False
            
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            
        return success
        
    except Exception as e:
        logger.error(f"✗ 文件操作测试失败: {e}")
        return False

def test_scripts_exist():
    """测试脚本文件是否存在"""
    logger.info("=== 测试脚本文件 ===")
    
    required_files = [
        'classyfire_submit.py',
        'classyfire_collect.py',
        'quick_start.py',
        'requirements.txt'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            logger.info(f"✓ {file} 存在")
        else:
            logger.error(f"✗ {file} 不存在")
            all_exist = False
    
    return all_exist

def test_input_file():
    """测试输入文件"""
    logger.info("=== 测试输入文件 ===")
    
    input_file = 'IECSC_WangHB.xlsx'
    
    if not os.path.exists(input_file):
        logger.warning(f"⚠ 输入文件 {input_file} 不存在")
        return False
    
    try:
        df = pd.read_excel(input_file)
        logger.info(f"✓ 输入文件读取成功，共 {len(df)} 行")
        
        # 检查是否有SMILES列
        smiles_columns = [col for col in df.columns if 'smiles' in col.lower()]
        if smiles_columns:
            logger.info(f"✓ 找到可能的SMILES列: {smiles_columns}")
            return True
        else:
            logger.warning("⚠ 未找到明显的SMILES列")
            logger.info(f"可用列: {list(df.columns)}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 输入文件读取失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行基本功能测试...")
    logger.info("=" * 50)
    
    tests = [
        ("依赖包", test_dependencies),
        ("脚本文件", test_scripts_exist),
        ("文件操作", test_file_operations),
        ("输入文件", test_input_file),
        ("网络连接", test_network)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
        
        logger.info("-" * 30)
    
    # 汇总结果
    logger.info("=== 测试结果汇总 ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("✓ 所有测试通过，工具可以正常使用")
        logger.info("\n下一步:")
        logger.info("1. 运行 python quick_start.py 开始使用")
        logger.info("2. 或直接运行 python classyfire_submit.py IECSC_WangHB.xlsx")
        return True
    else:
        logger.warning("✗ 部分测试失败，请检查配置")
        if passed >= 3:  # 至少基本功能正常
            logger.info("\n基本功能正常，可以尝试使用")
        return False

def main():
    """主函数"""
    print("ClassyFire工具基本功能测试")
    print("=" * 50)
    
    success = run_all_tests()
    
    if success:
        print("\n🎉 测试完成，工具可以正常使用！")
    else:
        print("\n⚠️  部分测试失败，请检查环境配置")

if __name__ == "__main__":
    main()
