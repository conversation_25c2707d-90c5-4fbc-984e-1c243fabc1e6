#!/usr/bin/env python3
"""
ClassyFire提交脚本

专门用于提交SMILES到ClassyFire进行分类，生成查询ID。
"""

import requests
import pandas as pd
import time
import json
import os
import logging
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire_submit.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireSubmitter:
    """ClassyFire提交器"""
    
    def __init__(self, batch_size: int = 50, delay: float = 5.0):
        self.base_url = "http://classyfire.wishartlab.com"
        self.batch_size = batch_size
        self.delay = delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0',
            'Content-Type': 'application/json'
        })
        
    def submit_query(self, structures: List[str], label: str = None) -> Optional[int]:
        """
        提交分类查询
        
        Args:
            structures: SMILES字符串列表
            label: 查询标签
            
        Returns:
            查询ID，失败返回None
        """
        try:
            if not label:
                label = f"Batch_Classification_{int(time.time())}"
                
            query_data = {
                "label": label,
                "query_input": "\n".join(structures),
                "query_type": "STRUCTURE"
            }
            
            url = f"{self.base_url}/queries.json"
            response = self.session.post(url, json=query_data, timeout=30)
            
            if response.status_code == 201:
                result = response.json()
                query_id = result.get('id')
                logger.info(f"成功提交查询，ID: {query_id}, 标签: {label}")
                return query_id
            else:
                logger.error(f"提交查询失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"提交查询时发生错误: {e}")
            return None
    
    def process_excel_file(self, input_file: str, smiles_column: str = 'smiles') -> List[int]:
        """
        处理Excel文件并提交所有SMILES
        
        Args:
            input_file: 输入Excel文件路径
            smiles_column: SMILES列名
            
        Returns:
            成功提交的查询ID列表
        """
        try:
            # 读取输入文件
            logger.info(f"读取输入文件: {input_file}")
            df = pd.read_excel(input_file)
            
            if smiles_column not in df.columns:
                logger.error(f"未找到SMILES列: {smiles_column}")
                return []
            
            # 过滤有效的SMILES
            df = df.dropna(subset=[smiles_column])
            df = df[df[smiles_column].str.len() <= 2000]  # 过滤过长的SMILES
            
            logger.info(f"共有 {len(df)} 个有效的SMILES需要分类")
            
            # 分批提交
            smiles_list = df[smiles_column].tolist()
            query_ids = []
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size
            
            for i in range(total_batches):
                start_idx = i * self.batch_size
                end_idx = min((i + 1) * self.batch_size, len(smiles_list))
                batch_smiles = smiles_list[start_idx:end_idx]
                
                logger.info(f"提交第 {i+1}/{total_batches} 批，包含 {len(batch_smiles)} 个化合物")
                
                # 提交查询
                label = f"Batch_{i+1}_{os.path.basename(input_file)}_{int(time.time())}"
                query_id = self.submit_query(batch_smiles, label)
                
                if query_id:
                    query_ids.append(query_id)
                    logger.info(f"成功提交查询 {query_id}")
                else:
                    logger.error(f"提交第 {i+1} 批失败")
                    break
                
                # 延迟以避免API限制
                if i < total_batches - 1:
                    logger.info(f"等待 {self.delay} 秒...")
                    time.sleep(self.delay)
            
            # 保存查询ID到文件
            if query_ids:
                query_file = f"{os.path.splitext(input_file)[0]}_query_ids.txt"
                with open(query_file, 'w') as f:
                    for qid in query_ids:
                        f.write(f"{qid}\n")
                logger.info(f"查询ID已保存到: {query_file}")
                
                # 同时保存为JSON格式，包含更多信息
                query_info = {
                    'input_file': input_file,
                    'smiles_column': smiles_column,
                    'total_compounds': len(smiles_list),
                    'batch_size': self.batch_size,
                    'query_ids': query_ids,
                    'submit_time': time.time()
                }
                
                json_file = f"{os.path.splitext(input_file)[0]}_query_info.json"
                with open(json_file, 'w') as f:
                    json.dump(query_info, f, indent=2)
                logger.info(f"查询信息已保存到: {json_file}")
            
            return query_ids
            
        except Exception as e:
            logger.error(f"处理文件时发生错误: {e}")
            return []

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python classyfire_submit.py <excel_file> [smiles_column] [batch_size] [delay]")
        print("示例: python classyfire_submit.py IECSC_WangHB.xlsx smiles 50 5")
        return
    
    input_file = sys.argv[1]
    smiles_column = sys.argv[2] if len(sys.argv) > 2 else 'smiles'
    batch_size = int(sys.argv[3]) if len(sys.argv) > 3 else 50
    delay = float(sys.argv[4]) if len(sys.argv) > 4 else 5.0
    
    if not os.path.exists(input_file):
        logger.error(f"输入文件不存在: {input_file}")
        return
    
    logger.info(f"输入文件: {input_file}")
    logger.info(f"SMILES列: {smiles_column}")
    logger.info(f"批处理大小: {batch_size}")
    logger.info(f"提交延迟: {delay}秒")
    
    submitter = ClassyFireSubmitter(batch_size=batch_size, delay=delay)
    query_ids = submitter.process_excel_file(input_file, smiles_column)
    
    if query_ids:
        logger.info(f"提交完成！共提交 {len(query_ids)} 个查询")
        logger.info(f"查询ID: {query_ids}")
        logger.info("请使用 classyfire_collect.py 收集结果")
    else:
        logger.error("提交失败！")

if __name__ == "__main__":
    main()
