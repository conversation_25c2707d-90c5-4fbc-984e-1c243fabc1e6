import pandas as pd
from rdkit import Chem
import sys

# --- 分类逻辑核心 (与之前的代码相同) ---
# 定义化学类别的 SMARTS 规则
CLASSIFICATION_RULES = {
    'Alcohol': '[#6][OH1]', 
    'Phenol': '[c][OH1]',
    'Carboxylic Acid': '[CX3](=O)[OX2H1]',
    'Ester': '[#6][CX3](=O)[OX1][#6]',
    'Ether': '[OD2]([#6])[#6]',
    'Amine': '[NX3;H2,H1,H0;!$(N=O)]',
    'Amide': '[C](=[O])[N]',
    'Ketone': '[#6][C](=[O])[#6]',
    'Aldehyde': '[CX3H1](=O)',
    'Aromatic Ring': 'a',
    'Halogen Compound': '[F,Cl,Br,I]',
    'Nitro Compound': '[$([NX3](=O)=O),$([NX3+](=O)[O-])]',
    'Thiol': '[#6][SH]',
    'Sulfonamide': '[S](=[O])(=[O])N'
}

# 预编译 SMARTS 模式以提高效率
COMPILED_RULES = {name: Chem.MolFromSmarts(smarts) for name, smarts in CLASSIFICATION_RULES.items()}

def classify_chemical_from_smiles(smiles: str) -> list:
    """
    根据 SMILES 字符串识别化学品所属的类别。
    """
    # 处理空的或非字符串输入
    if not isinstance(smiles, str) or smiles.strip() == '':
        return ["Invalid Input"]

    mol = Chem.MolFromSmiles(smiles)

    if mol is None:
        return [f"Invalid SMILES"]

    found_categories = []
    for name, pattern in COMPILED_RULES.items():
        if mol.HasSubstructMatch(pattern):
            found_categories.append(name)
            
    if not found_categories:
        return ["Uncategorized"]

    return found_categories

# --- Excel 文件处理主函数 ---
def process_excel_file(input_path: str, output_path: str, smiles_column: str, new_column_name: str = 'Category'):
    """
    读取Excel文件，根据SMILES列进行分类，并写入新的Excel文件。
    """
    print(f"--- 开始处理文件: {input_path} ---")
    
    # 1. 读取 Excel 文件
    try:
        df = pd.read_excel(input_path)
        print(f"成功读取文件，共 {len(df)} 行数据。")
    except FileNotFoundError:
        print(f"错误：找不到文件 '{input_path}'。请检查文件名和路径。")
        sys.exit(1)
    except Exception as e:
        print(f"读取 Excel 文件时发生错误: {e}")
        sys.exit(1)

    # 2. 检查 SMILES 列是否存在
    if smiles_column not in df.columns:
        print(f"错误：在文件中找不到名为 '{smiles_column}' 的列。")
        print(f"可用的列有: {list(df.columns)}")
        sys.exit(1)

    print(f"使用 '{smiles_column}' 列中的SMILES进行分类...")
    
    # 3. 应用分类函数到 SMILES 列
    # .apply() 会将每一行的smiles作为参数传递给 classify_chemical_from_smiles 函数
    # lambda 函数用于将返回的列表转换成一个用逗号分隔的字符串
    categories = df[smiles_column].apply(
        lambda smi: ', '.join(classify_chemical_from_smiles(smi))
    )
    
    # 4. 将结果作为新列添加到 DataFrame
    df[new_column_name] = categories
    print("分类完成，已生成新的类别列。")

    # 5. 保存到新的 Excel 文件
    try:
        # index=False 表示不将 DataFrame 的索引写入 Excel 文件
        df.to_excel(output_path, index=False)
        print(f"--- 处理完成！结果已保存到: {output_path} ---")
    except Exception as e:
        print(f"保存到 Excel 文件时发生错误: {e}")
        sys.exit(1)


# --- 用户配置区域 ---
if __name__ == "__main__":
    # 1. 设置输入文件名
    INPUT_FILE = 'prepro_FishLC50_scr.xlsx'

    # 2. 设置输出文件名
    OUTPUT_FILE = 'prepro_FishLC50_scr_classified.xlsx'

    # 3. 指定包含SMILES的列的名称 (必须与Excel中的表头完全一致)
    SMILES_COLUMN_NAME = 'Canonical smiles'

    # 运行主程序
    process_excel_file(
        input_path=INPUT_FILE,
        output_path=OUTPUT_FILE,
        smiles_column=SMILES_COLUMN_NAME
    )