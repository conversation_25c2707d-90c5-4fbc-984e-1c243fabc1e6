import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from sklearn import metrics

from adsal import NSG, NSGVisualizer

# =============================================================================
# 配置区域 - 可自定义设置
# =============================================================================
# 输出文件夹设置（可根据需要修改此路径）
Endpoint = 'AlgAT'           # FishAT, FishCT, DMCT, DMAT, AlgAT
OUTPUT_DIR = f'AD_{Endpoint}'  # 默认输出文件夹名称


# 确保输出文件夹存在
def ensure_output_dir(output_dir):
    """确保输出文件夹存在，如果不存在则创建"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出文件夹: {output_dir}")
    return output_dir

def rigidWt(x, sCutoff=0.85):
    y = np.ones(shape=x.shape)
    y[x < sCutoff] = 0
    return y
#
def expWt(x, a=10, eps=1e-10):
    # a = 3, Liu et al. JCIM, 2018
    return np.exp(-a*(1-x)/(x + eps))
#
wtFunc1a = rigidWt
kw1a = {'sCutoff':0.80}
wtFunc2a = rigidWt
kw2a = {'sCutoff':0.80}
wtFunc1b = expWt
kw1b = {'a':6}
wtFunc2b = expWt
kw2b = {'a':6}

#import data
# 确保输出文件夹存在
output_folder = ensure_output_dir(OUTPUT_DIR)

# load training set data
dfTr = pd.read_excel(f'TrainingSet_{Endpoint}.xlsx', index_col = 'index')
# load testing set data
dfEx = pd.read_excel(f'ExternalSet_pred_{Endpoint}.xlsx', index_col = 'index')

# NSG
nsg = NSG(dfTr,yCol='y',smiCol='smiles')
# nsg.calcPairwiseSimilarityWithFp('Morgan(bit)',radius=2, nBits=1024)
nsg.calcPairwiseSimilarityWithFp('MACCS_keys')
dfQTSM = nsg.genQTSM(dfEx,'smiles')
#dfTr = dfTr[['neuSmi','y']]
#dfEx = dfEx[['neuSmi','y_true']]
dfEx = dfEx.join(nsg.queryADMetrics(dfQTSM
                                    , wtFunc1=wtFunc1a,kw1=kw1a
                                    , wtFunc2=wtFunc2a,kw2=kw2a
                                    ,code='|rigid'))
dfEx = dfEx.join(nsg.queryADMetrics(dfQTSM
                                    , wtFunc1=wtFunc1b,kw1=kw1b
                                    , wtFunc2=wtFunc2b,kw2=kw2b
                                    ,code='|exp'))

# 保存 AD 指标到指定文件夹
dfEx.to_csv(os.path.join(output_folder, f'dfEx_ADMetrics_{Endpoint}.csv'))

dfPlot = pd.read_csv(os.path.join(output_folder, f'dfEx_ADMetrics_{Endpoint}.csv'))
densDict = {
'rigid':[1, 2, 3, 5, 12, 20],
'exp':[0.00001, 0.001, 0.01, 0.1, 0.2, 0.3, 0.4, 0.5]}   #相似性密度计算

yt = dfPlot['y_true']
yprob = dfPlot['yExt_probA']
yp = (yprob > 0.5).astype(int)
IAValList = [ 0.1, 0.2, 0.3, 0.4, 0.5,  0.6, 0.7,]  #局域不连续性

for code in ['rigid','exp']:
    dfn = pd.DataFrame(index=IAValList,columns=densDict[code])
    dfAUC = pd.DataFrame(index=IAValList,columns=densDict[code])
    dfBA = pd.DataFrame(index=IAValList,columns=densDict[code])
    dfP = pd.DataFrame(index=IAValList,columns=densDict[code])
    dfR = pd.DataFrame(index=IAValList,columns=densDict[code])
    dfLL = pd.DataFrame(index=IAValList,columns=densDict[code])
    for densLB in dfAUC.columns:
        for LdUB in dfAUC.index:
            adi = dfPlot.index[(dfPlot['simiDensity|'+code] >= densLB)&(dfPlot['simiWtLD_w|'+code] <= LdUB)]
            # 记录样本数
            cnt = int(len(adi))
            dfn.loc[LdUB, densLB] = cnt
            # 空样本直接写 NaN 并跳过，避免各类度量报错
            if cnt == 0:
                dfAUC.loc[LdUB, densLB] = np.nan
                dfBA.loc[LdUB, densLB] = np.nan
                dfP.loc[LdUB, densLB] = np.nan
                dfR.loc[LdUB, densLB] = np.nan
                dfLL.loc[LdUB, densLB] = np.nan
                continue
            # 提取子集
            y_true = yt[adi]
            y_prob = yprob[adi]
            y_pred = yp[adi]
            # AUC（类别不足时返回 NaN）
            try:
                dfAUC.loc[LdUB, densLB] = metrics.roc_auc_score(y_true, y_prob)
            except Exception:
                dfAUC.loc[LdUB, densLB] = np.nan
            # 平衡准确率
            try:
                dfBA.loc[LdUB, densLB] = metrics.balanced_accuracy_score(y_true, y_pred)
            except Exception:
                dfBA.loc[LdUB, densLB] = np.nan
            # 精确率/召回率（避免除零问题）
            try:
                dfP.loc[LdUB, densLB] = metrics.precision_score(y_true, y_pred, zero_division=0)
            except Exception:
                dfP.loc[LdUB, densLB] = np.nan
            try:
                dfR.loc[LdUB, densLB] = metrics.recall_score(y_true, y_pred, zero_division=0)
            except Exception:
                dfR.loc[LdUB, densLB] = np.nan
            # 对数损失（提供 labels 并在异常时置 NaN）
            try:
                dfLL.loc[LdUB, densLB] = metrics.log_loss(y_true, y_prob, labels=[1,0])
            except Exception:
                dfLL.loc[LdUB, densLB] = np.nan
            #
    # 保存所有结果文件到指定文件夹
    dfn.to_csv(os.path.join(output_folder, 'model_{:s}_AD_n.csv'.format(code)))
    dfAUC.to_csv(os.path.join(output_folder, 'model_{:s}_AD_AROC.csv'.format(code)))
    dfBA.to_csv(os.path.join(output_folder, 'model_{:s}_AD_RBA.csv'.format(code)))
    dfP.to_csv(os.path.join(output_folder, 'model_{:s}_AD_P.csv'.format(code)))
    dfR.to_csv(os.path.join(output_folder, 'model_{:s}_AD_R.csv'.format(code)))
    dfLL.to_csv(os.path.join(output_folder, 'model_{:s}_AD_LL.csv'.format(code)))

