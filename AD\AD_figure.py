import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import os

# 设置全局字体为Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学公式字体

# =============================================================================
# 配置区域 - 可自定义设置
# =============================================================================
# 输出图片文件夹设置
Endpoint = 'FishAT'  # FishAT, FishCT, DMCT, DMAT, AlgAT
Task = 'AROC'         # 'n' , 'R2', 'RMSE', 'MAE, 'F1', 'AROC', 'RBA'
Wtmethod = 'exp'     # 'rigid', 'exp'
OUTPUT_DIR = f'AD_{Endpoint}'  # 图片输出文件夹名称

# 确保输出文件夹存在
def ensure_output_dir(output_dir):
    """确保输出文件夹存在，如果不存在则创建"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"已创建输出文件夹: {output_dir}")
    return output_dir

# 创建输出文件夹
output_folder = ensure_output_dir(OUTPUT_DIR)

# 读取数据
file_name = f'model_{Wtmethod}_AD_{Task}.csv'
input_file_path = os.path.join(OUTPUT_DIR, file_name)

df = pd.read_csv(input_file_path, index_col=0)
print(f"成功读取数据文件: {input_file_path}")

# --------------------------
# 数据处理
# --------------------------
x = df.index.astype(float).values
y = df.columns.astype(float).values
z = df.values.T

X, Y = np.meshgrid(x, y)

# --------------------------
# 绘制3D曲面图
# --------------------------
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')

surf = ax.plot_surface(
    X, Y, z,
    cmap='viridis',
    rcount=100,
    ccount=100,
    edgecolor='none',
    alpha=0.8,
    vmin=z.min(),
    vmax=z.max()
)

# 颜色条设置
cbar = fig.colorbar(surf, ax=ax, shrink=0.6, aspect=10)
# cbar.set_label(r'$n$', rotation=0, labelpad=20, fontsize=14)
cbar.set_label(r'$A_{\rm ROC}$', rotation=0, labelpad=20, fontsize=14)
# cbar.set_label(r'$R_{\rm BA}$', rotation=0, labelpad=20, fontsize=14)
cbar.ax.tick_params(labelsize=12)  # 颜色条刻度字体大小

# 轴标签设置
ax.set_xlabel('$I_{\mathrm{A,T}}$', fontsize=14, labelpad=12)
ax.set_ylabel('$ρ_{\mathrm{s,T}}$', fontsize=14, labelpad=12)
# ax.set_zlabel(r'$n$', fontsize=14, labelpad=12)
# 设置z轴标签为水平显示
ax.zaxis.set_rotate_label(False)  # 禁用自动旋转
ax.set_zlabel(r'$A_{\rm ROC}$', fontsize=14, labelpad=12, rotation=0)  # 强制水平旋转
# ax.set_zlabel(r'$R_{\rm BA}$', fontsize=14, labelpad=12, rotation=0)

# 刻度标签设置
ax.tick_params(axis='x', labelsize=12)
ax.tick_params(axis='y', labelsize=12)
ax.tick_params(axis='z', labelsize=12)

# 视角和标题
ax.view_init(elev=30, azim=225)
# plt.title(r'Number of compounds ($n$) retained in the AD ', fontsize=16, pad=20)
# plt.title(r'3D Surface Plot of $n$', fontsize=16, pad=20)
# plt.title(r'3D Surface Plot of $R_{\rm BA}$', fontsize=16, pad=20)

# 保存和显示
output_file_path = os.path.join(output_folder, f'AD_{Endpoint}_model_{Wtmethod}_AD_{Task}.png')
plt.savefig(output_file_path, dpi=300, bbox_inches='tight')
print(f"图片已保存到: {output_file_path}")
plt.show()