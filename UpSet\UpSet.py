import pandas as pd
import matplotlib.pyplot as plt
from upsetplot import UpSet, from_contents
import warnings

# 忽略 pandas 的 FutureWarning，因为它只是一个警告，不影响结果
warnings.filterwarnings("ignore", category=FutureWarning, module="upsetplot.data")

# --- 1. 环境设置与样式定义 ---
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

NATURE_STYLE_COLORS = {
    'FihAT':  '#78C8C9',
    'AlaAT':  '#F6A099',
    'DMAT':   '#F8D49B',
    'DMCT':   '#A9D3F8',
    'FishCT': '#BEA2D6'
}

def create_nature_style_upset(file_mapping, custom_colors):
    """
    创建具有清晰行分隔和自定义颜色的Nature风格UpSet图
    """
    
    # --- 2. 数据读取 ---
    print("🔄 开始读取数据...")
    smiles_sets = {}
    for label, file_path in file_mapping.items():
        try:
            df = pd.read_excel(file_path)
            smiles_col = next((col for col in df.columns if 'smiles' in col.lower().replace(" ", "")), None)
            if smiles_col:
                smiles_sets[label] = set(df[smiles_col].dropna().astype(str))
                print(f"✅ {label:8s} | {len(smiles_sets[label]):5,d} 个唯一SMILES")
            else:
                print(f"⚠️  {label:8s} | 未找到SMILES列")
        except Exception as e:
            print(f"❌ {label:8s} | 读取失败: {e}")
    
    if not smiles_sets:
        print("❌ 错误: 未能成功读取任何数据")
        return

    # --- 3. 准备UpSet数据 ---
    upset_data = from_contents(smiles_sets)
    print("✅ 数据准备完成")

    # --- 4. 绘图核心 ---
    print("🎨 开始绘制Nature风格UpSet图...")
    
    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10
    plt.rcParams['figure.titlesize'] = 16
    
    fig = plt.figure(figsize=(12, 8))

    # **修正后的UpSet对象创建**：
    # 移除不支持的 plot_elements 参数
    upset = UpSet(
        upset_data,
        sort_by='cardinality',
        sort_categories_by=None,
        min_subset_size=40,
        show_counts=True,
        facecolor='#6495ED',
        shading_color='#f0f0f0',
        with_lines=True,
        element_size=None
    )
    
    # 绘制图形，并返回包含所有子图axes的字典
    axes_dict = upset.plot(fig=fig)

    # --- 5. 精细化样式调整 ---
    print("🎨 进行精细化样式调整...")
    
    # 自定义总计柱状图的颜色
    if 'totals' in axes_dict:
        # 获取类别名称（通过数据的索引）
        category_names = upset_data.index.names
        patches = axes_dict['totals'].patches
        
        for i, patch in enumerate(patches):
            if i < len(category_names):
                label = category_names[i]
                patch.set_facecolor(custom_colors.get(label, 'gray'))
                patch.set_edgecolor('white')
                patch.set_linewidth(0.5)

    # 自定义交集柱状图的颜色
    if 'intersections' in axes_dict:
        for patch in axes_dict['intersections'].patches:
            patch.set_facecolor(NATURE_STYLE_COLORS['FihAT'])
            patch.set_edgecolor('white')
            patch.set_linewidth(0.5)

    # 自定义矩阵中的点和线的样式
    if 'matrix' in axes_dict:
        # 获取矩阵图中的所有线条和点
        for line in axes_dict['matrix'].lines:
            line.set_color('#333333')
            line.set_linewidth(2)
        
        # 获取所有散点图集合并自定义颜色
        for collection in axes_dict['matrix'].collections:
            # 设置存在的点为深色，不存在的点为浅色
            facecolors = []
            for i in range(len(collection.get_offsets())):
                # 这里可以根据需要进一步自定义点的颜色逻辑
                facecolors.append('#333333')  # 深灰色表示存在
            # collection.set_facecolors(facecolors)

    # 移除图例（如果存在）
    if 'legend' in axes_dict and axes_dict['legend'] is not None:
        axes_dict['legend'].remove()

    # 添加标题
    plt.suptitle('Visualization of Molecular Structure Overlap Across Five Endpoint Datasets', 
                fontsize=16, fontweight='bold', y=0.98, fontfamily='Times New Roman')
    # fig.text(0.5, 0.92, 'UpSet Analysis with Nature-Style Visualization', 
    #         ha='center', fontsize=12, color='gray', fontfamily='Times New Roman')
    
    # --- 6. 美化坐标轴 ---
    # 美化总计柱状图
    if 'totals' in axes_dict:
        axes_dict['totals'].grid(True, alpha=0.3, linestyle='--')
        axes_dict['totals'].set_xlabel('Dataset Size', fontsize=12, fontweight='bold', 
                                      fontfamily='Times New Roman')
        axes_dict['totals'].tick_params(axis='x', labelsize=10)
        axes_dict['totals'].tick_params(axis='y', labelsize=10)
        # 设置刻度标签字体
        for label in axes_dict['totals'].get_xticklabels():
            label.set_fontfamily('Times New Roman')
        for label in axes_dict['totals'].get_yticklabels():
            label.set_fontfamily('Times New Roman')
    
    # 美化交集柱状图
    if 'intersections' in axes_dict:
        axes_dict['intersections'].grid(True, alpha=0.3, linestyle='--')
        axes_dict['intersections'].set_ylabel('Unique and Intersection Size', fontsize=12, fontweight='bold',
                                            fontfamily='Times New Roman')
        axes_dict['intersections'].tick_params(axis='both', labelsize=10)
        # 设置刻度标签字体
        for label in axes_dict['intersections'].get_xticklabels():
            label.set_fontfamily('Times New Roman')
        for label in axes_dict['intersections'].get_yticklabels():
            label.set_fontfamily('Times New Roman')
    
    # 美化矩阵图标签
    if 'matrix' in axes_dict:
        axes_dict['matrix'].tick_params(axis='both', labelsize=10)
        # 设置矩阵图的刻度标签字体
        for label in axes_dict['matrix'].get_xticklabels():
            label.set_fontfamily('Times New Roman')
        for label in axes_dict['matrix'].get_yticklabels():
            label.set_fontfamily('Times New Roman')
    
    # --- 7. 保存图片 ---
    plt.tight_layout()
    plt.savefig('UpSet_SMILES.png', dpi=300, bbox_inches='tight')
    plt.savefig('UpSet_SMILES.pdf', bbox_inches='tight')
    print("✅ 图表已保存为 UpSet_SMILES.png/pdf")
    
    plt.show()

# --- 主程序 ---
if __name__ == "__main__":
    file_mapping = {
        "FihAT":  "prepro_FishLC50_scr.xlsx",
        "AlaAT":  "prepro_AlageGroErC50_scr.xlsx",
        "DMAT":   "prepro_DMImbEC50_scr.xlsx",
        "DMCT":   "prepro_DMRepNOEC_scr.xlsx",
        "FishCT": "prepro_FishGroNOEC_scr.xlsx",
    }
    
    create_nature_style_upset(file_mapping, custom_colors=NATURE_STYLE_COLORS)
    print("\n🎉 Nature风格UpSet图创建完成！")