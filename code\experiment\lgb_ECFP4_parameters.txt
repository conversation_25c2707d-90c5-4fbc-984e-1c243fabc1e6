FishLC50:{'max_depth': 7, 'min_child_samples': 40, 'reg_lambda': 0.01, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 260, 'feature_fraction': 0.75, 'bagging_fraction': 0.75, 'num_leaves': 91}
FishEL_NOEC:{'max_depth': 9, 'min_child_samples': 20, 'reg_lambda': 1e-05, 'reg_alpha': 0.1, 'learning_rate': 0.05, 'n_estimators': 100, 'feature_fraction': 0.85, 'bagging_fraction': 0.85, 'num_leaves': 71}
DMRepNOEC:{'max_depth': 9, 'min_child_samples': 20, 'reg_lambda': 0.01, 'reg_alpha': 1e-05, 'learning_rate': 0.05, 'n_estimators': 240, 'feature_fraction': 0.8, 'bagging_fraction': 0.85, 'num_leaves': 51}
DMImbEC50:{'max_depth': 9, 'min_child_samples': 50, 'reg_lambda': 0.1, 'reg_alpha': 0.01, 'learning_rate': 0.05, 'n_estimators': 220, 'feature_fraction': 0.85, 'bagging_fraction': 0.85, 'num_leaves': 81}
AlaGroErC50:{'max_depth': 5, 'min_child_samples': 30, 'reg_lambda': 0.01, 'reg_alpha': 1e-05, 'learning_rate': 0.05, 'n_estimators': 200, 'feature_fraction': 0.8, 'bagging_fraction': 0.8, 'num_leaves': 81}
