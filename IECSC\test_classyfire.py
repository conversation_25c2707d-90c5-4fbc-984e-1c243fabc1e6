#!/usr/bin/env python3
"""
ClassyFire测试脚本

用于测试ClassyFire工具的基本功能。
"""

import pandas as pd
import os
import sys
from classyfire_main import ClassyFireClient, ClassyFireBatchProcessor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_client_connection():
    """测试客户端连接"""
    logger.info("=== 测试客户端连接 ===")
    
    client = ClassyFireClient()
    
    # 测试通过InChIKey获取已知化合物（咖啡因）
    inchikey = "RYYVLZVUVIJVGH-UHFFFAOYSA-N"
    result = client.get_entity_by_inchikey(inchikey)
    
    if result:
        logger.info("✓ 客户端连接正常")
        logger.info(f"测试化合物: {result.get('name', 'Unknown')}")
        return True
    else:
        logger.error("✗ 客户端连接失败")
        return False

def test_data_processing():
    """测试数据处理功能"""
    logger.info("=== 测试数据处理功能 ===")
    
    # 创建测试数据
    test_data = {
        'compound_id': ['TEST001', 'TEST002', 'TEST003'],
        'smiles': [
            "CCO",                    # 乙醇
            "c1ccccc1",              # 苯
            "CC(=O)O"                # 乙酸
        ],
        'name': ['Ethanol', 'Benzene', 'Acetic acid']
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_compounds.xlsx'
    
    try:
        df.to_excel(test_file, index=False)
        logger.info(f"✓ 测试数据文件创建成功: {test_file}")
        
        # 测试读取
        df_read = pd.read_excel(test_file)
        if 'smiles' in df_read.columns and len(df_read) == 3:
            logger.info("✓ 数据读取功能正常")
            return True
        else:
            logger.error("✗ 数据读取功能异常")
            return False
            
    except Exception as e:
        logger.error(f"✗ 数据处理测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def test_batch_processor_init():
    """测试批量处理器初始化"""
    logger.info("=== 测试批量处理器初始化 ===")
    
    try:
        processor = ClassyFireBatchProcessor(batch_size=10, delay=1.0)
        
        if processor.client and processor.batch_size == 10 and processor.delay == 1.0:
            logger.info("✓ 批量处理器初始化成功")
            return True
        else:
            logger.error("✗ 批量处理器初始化失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ 批量处理器初始化异常: {e}")
        return False

def test_status_management():
    """测试状态管理功能"""
    logger.info("=== 测试状态管理功能 ===")
    
    processor = ClassyFireBatchProcessor()
    
    # 测试状态保存和加载
    test_status = {
        'query_ids': [12345, 67890],
        'submitted_count': 100,
        'results': {'CCO': {'kingdom_name': 'Organic compounds'}}
    }
    
    try:
        # 保存状态
        processor.save_status(test_status)
        
        # 加载状态
        loaded_status = processor.load_status()
        
        if (loaded_status.get('query_ids') == test_status['query_ids'] and
            loaded_status.get('submitted_count') == test_status['submitted_count']):
            logger.info("✓ 状态管理功能正常")
            return True
        else:
            logger.error("✗ 状态管理功能异常")
            return False
            
    except Exception as e:
        logger.error(f"✗ 状态管理测试失败: {e}")
        return False
    finally:
        # 清理状态文件
        if os.path.exists(processor.status_file):
            os.remove(processor.status_file)

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行ClassyFire工具测试...")
    
    tests = [
        ("客户端连接", test_client_connection),
        ("数据处理", test_data_processing),
        ("批量处理器初始化", test_batch_processor_init),
        ("状态管理", test_status_management)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 {test_name} 发生异常: {e}")
            results.append((test_name, False))
        
        logger.info("-" * 50)
    
    # 汇总结果
    logger.info("=== 测试结果汇总 ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("✓ 所有测试通过，工具可以正常使用")
        return True
    else:
        logger.warning("✗ 部分测试失败，请检查配置和网络连接")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # 快速测试，跳过网络连接测试
        logger.info("运行快速测试（跳过网络连接）...")
        tests = [
            ("数据处理", test_data_processing),
            ("批量处理器初始化", test_batch_processor_init),
            ("状态管理", test_status_management)
        ]
        
        results = []
        for test_name, test_func in tests:
            result = test_func()
            results.append((test_name, result))
            logger.info("-" * 30)
        
        passed = sum(1 for _, result in results if result)
        logger.info(f"快速测试结果: {passed}/{len(results)} 个测试通过")
    else:
        run_all_tests()

if __name__ == "__main__":
    main()
