FishLC50:{'max_depth': 8, 'min_child_weight': 1, 'gamma': 0.02, 'reg_lambda': 0.1, 'reg_alpha': 1e-05, 'lr': 0.05, 'n_estimators': 280, 'colsample_bytree': 0.8, 'subsample': 0.75}
FishEL_NOEC:{'max_depth': 6, 'min_child_weight': 1, 'gamma': 0.08, 'reg_lambda': 0.01, 'reg_alpha': 1, 'lr': 0.001, 'n_estimators': 100, 'colsample_bytree': 0.75, 'subsample': 0.75}
DMRepNOEC:{'max_depth': 3, 'min_child_weight': 5, 'gamma': 0.08, 'reg_lambda': 1e-05, 'reg_alpha': 0.1, 'lr': 0.05, 'n_estimators': 260, 'colsample_bytree': 0.75, 'subsample': 0.8}
DMImbEC50:{'max_depth': 9, 'min_child_weight': 4, 'gamma': 0.16, 'reg_lambda': 0.01, 'reg_alpha': 0.01, 'lr': 0.05, 'n_estimators': 120, 'colsample_bytree': 0.75, 'subsample': 0.8}
AlaGroErC50:{'max_depth': 6, 'min_child_weight': 1, 'gamma': 0.08, 'reg_lambda': 0.1, 'reg_alpha': 0.1, 'lr': 0.05, 'n_estimators': 260, 'colsample_bytree': 0.75, 'subsample': 0.85}
