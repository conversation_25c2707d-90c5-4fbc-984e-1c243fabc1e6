import pandas as pd
import requests
import time
import json
import pickle
import os
from typing import Dict, Optional

class ClassyFireClassifier:
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.status_file = "classyfire_status.pkl"

    def submit_compound(self, smiles: str) -> Optional[str]:
        """提交化合物到ClassyFire，返回查询ID"""
        if pd.isna(smiles) or not smiles.strip() or len(smiles) > 2000:
            return None

        try:
            data = {
                'label': 'compound',
                'smiles': smiles.strip(),
                'structure_source': 'SMILES'
            }
            response = self.session.post(f"{self.base_url}/entities.json",
                                       data=data, timeout=30)

            if response.status_code == 201:
                return response.json().get('id')
            elif response.status_code == 422:
                return None
            elif response.status_code == 429:
                time.sleep(10)
                return self.submit_compound(smiles)

        except Exception:
            pass
        return None

    def get_classification(self, query_id: str) -> Optional[Dict]:
        """获取分类结果"""
        try:
            response = self.session.get(f"{self.base_url}/entities/{query_id}.json",
                                      timeout=30)

            if response.status_code == 200:
                result = response.json()
                if any(key in result for key in ['kingdom', 'superclass', 'class']):
                    return self._extract_info(result)
            return None
        except Exception:
            return None

    def _extract_info(self, result: Dict) -> Dict:
        """提取分类信息"""
        info = {}
        levels = ['kingdom', 'superclass', 'class', 'subclass']

        for level in levels:
            if level in result and result[level]:
                info[f'{level}_name'] = result[level].get('name', '')
                info[f'{level}_id'] = result[level].get('chemont_id', '')

        for key in ['smiles', 'inchikey', 'molecular_formula']:
            if key in result:
                info[key] = result[key]

        return info
        
    def classify_compound(self, smiles: str, max_retries: int = 3, delay: float = 5.0) -> Optional[Dict]:
        """
        对单个化合物进行ClassyFire分类
        
        Args:
            smiles: SMILES字符串
            max_retries: 最大重试次数
            delay: 请求间隔时间（秒）
            
        Returns:
            分类结果字典，包含各级分类信息
        """
        if pd.isna(smiles) or not smiles.strip():
            return None
            
        # 清理和验证SMILES字符串
        smiles = smiles.strip()
        
        # 基本SMILES有效性检查
        if not self._is_valid_smiles_format(smiles):
            logger.warning(f"SMILES格式可能有问题: {smiles}")
            return {'error': 'Invalid SMILES format'}
        
        for attempt in range(max_retries):
            try:
                # 修正：按照文档格式提交查询
                submit_url = f"{self.base_url}/queries"
                
                # 按照API文档要求的格式构造数据
                data = {
                    'label': f'compound_{int(time.time())}_{attempt}',
                    'query_input': smiles,  # 直接使用SMILES字符串
                    'query_type': 'STRUCTURE'  # 明确指定查询类型
                }
                
                # 使用JSON格式提交
                response = self.session.post(submit_url, 
                                           json=data,  # 使用json参数而不是data
                                           timeout=60)
                
                logger.info(f"提交响应状态: {response.status_code}")
                logger.info(f"响应内容: {response.text[:500]}")
                
                if response.status_code in [200, 201]:
                    try:
                        result = response.json()
                        query_id = result.get('id')
                        
                        if query_id:
                            logger.info(f"获得查询ID: {query_id}")
                            # 等待处理完成后获取结果
                            time.sleep(3)  # 给服务器处理时间
                            classification = self._get_classification_result(query_id)
                            if classification:
                                return classification
                        else:
                            logger.error(f"未获得查询ID，响应: {result}")
                    except json.JSONDecodeError:
                        logger.error(f"响应不是有效JSON: {response.text}")
                        
                elif response.status_code == 422:
                    logger.error(f"SMILES无效或无法处理: {smiles}")
                    return {'error': 'Unprocessable SMILES'}
                else:
                    logger.warning(f"提交失败，状态码: {response.status_code}, 响应: {response.text}")
                
                # 如果失败，等待后重试
                if attempt < max_retries - 1:
                    wait_time = delay * (2 ** attempt)  # 指数退避
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
            except requests.exceptions.ConnectionError:
                logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries})")
            except Exception as e:
                logger.warning(f"分类SMILES '{smiles}' 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                
            if attempt < max_retries - 1:
                time.sleep(delay * (attempt + 1))
                    
        logger.error(f"所有重试失败，无法分类SMILES: {smiles}")
        return {'error': 'Classification failed after retries'}
    
    def _is_valid_smiles_format(self, smiles: str) -> bool:
        """
        基本SMILES格式验证
        """
        if not smiles:
            return False
        
        # 检查括号匹配
        open_brackets = smiles.count('(')
        close_brackets = smiles.count(')')
        if open_brackets != close_brackets:
            return False
        
        # 检查方括号匹配
        open_square = smiles.count('[')
        close_square = smiles.count(']')
        if open_square != close_square:
            return False
        
        # 检查是否以无效字符开始
        if smiles.startswith((')', ']', '=')):
            return False
            
        return True
    
    def _get_classification_result(self, query_id: str, max_wait: int = 120) -> Optional[Dict]:
        """
        获取ClassyFire分类结果
        
        Args:
            query_id: 查询ID
            max_wait: 最大等待时间（秒）
            
        Returns:
            分类结果字典
        """
        start_time = time.time()
        check_interval = 5  # 检查间隔增加到5秒
        
        while time.time() - start_time < max_wait:
            try:
                # 修正：使用queries端点获取结果
                result_url = f"{self.base_url}/queries/{query_id}.json"
                response = self.session.get(result_url, timeout=30)
                
                logger.info(f"查询状态: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 检查查询状态
                    query_state = result.get('classification_status', '').lower()
                    logger.info(f"分类状态: {query_state}")
                    
                    if query_state == 'done':
                        # 获取实体数据
                        entities = result.get('entities', [])
                        if entities and len(entities) > 0:
                            entity = entities[0]  # 取第一个实体
                            return self._extract_classification_info(entity)
                    elif query_state in ['failed', 'error']:
                        logger.error(f"分类失败: {result}")
                        return {'error': 'Classification failed'}
                    else:
                        logger.info(f"分类进行中...状态: {query_state}")
                
                # 等待处理完成
                time.sleep(check_interval)
                
            except Exception as e:
                logger.warning(f"获取结果时出错: {str(e)}")
                time.sleep(check_interval)
                
        logger.warning(f"查询超时: {query_id}")
        return None
    
    def _extract_classification_info(self, entity: Dict) -> Dict:
        """
        从ClassyFire实体结果中提取分类信息
        
        Args:
            entity: ClassyFire API返回的实体数据
            
        Returns:
            整理后的分类信息字典
        """
        classification = {}
        
        # 提取各级分类信息
        hierarchy_levels = [
            'kingdom', 'superclass', 'class', 'subclass', 
            'molecular_framework', 'alternative_parents', 
            'substituents', 'direct_parent'
        ]
        
        for level in hierarchy_levels:
            if level in entity and entity[level]:
                if level == 'alternative_parents':
                    # 处理替代父类（可能有多个）
                    if isinstance(entity[level], list) and entity[level]:
                        names = [item.get('name', '') for item in entity[level] if item.get('name')]
                        classification[f"{level}_names"] = '; '.join(names[:3])  # 取前3个
                elif level == 'substituents':
                    # 处理取代基（可能有多个）
                    if isinstance(entity[level], list) and entity[level]:
                        names = [item.get('name', '') for item in entity[level] if item.get('name')]
                        classification[f"{level}_names"] = '; '.join(names[:5])  # 取前5个
                else:
                    # 处理单个分类项
                    if isinstance(entity[level], dict):
                        classification[f"{level}_name"] = entity[level].get('name', '')
                        classification[f"{level}_chemont_id"] = entity[level].get('chemont_id', '')
        
        # 添加其他有用信息
        if 'smiles' in entity:
            classification['canonical_smiles'] = entity['smiles']
        if 'inchikey' in entity:
            classification['inchikey'] = entity['inchikey']
        if 'molecular_formula' in entity:
            classification['molecular_formula'] = entity['molecular_formula']
        if 'identifier' in entity:
            classification['identifier'] = entity['identifier']
            
        return classification

    def test_single_compound(self, smiles: str) -> None:
        """
        测试单个化合物分类
        """
        logger.info(f"测试分类SMILES: {smiles}")
        result = self.classify_compound(smiles)
        if result:
            logger.info(f"分类结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            logger.error("分类失败")

def classify_excel_compounds(input_file: str, 
                           output_file: str, 
                           smiles_column: str = 'smiles',
                           batch_delay: float = 3.0,
                           start_row: int = 0,
                           max_rows: Optional[int] = None) -> None:
    """
    对Excel文件中的化合物进行批量ClassyFire分类
    
    Args:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
        smiles_column: SMILES列名
        batch_delay: 批处理延迟时间（秒），建议至少3秒
        start_row: 开始处理的行号
        max_rows: 最大处理行数，None表示处理所有
    """
    
    # 读取Excel文件
    try:
        df = pd.read_excel(input_file)
        logger.info(f"成功读取文件: {input_file}, 共 {len(df)} 行数据")
    except Exception as e:
        logger.error(f"读取文件失败: {str(e)}")
        return
    
    # 检查SMILES列是否存在
    if smiles_column not in df.columns:
        logger.error(f"未找到列 '{smiles_column}', 可用列: {list(df.columns)}")
        return
    
    # 限制处理范围
    if max_rows:
        end_row = min(start_row + max_rows, len(df))
    else:
        end_row = len(df)
    
    df_subset = df.iloc[start_row:end_row].copy()
    logger.info(f"处理行范围: {start_row} - {end_row-1}")
    
    # 初始化分类器
    classifier = ClassyFireClassifier()
    
    # 准备结果列
    classification_columns = [
        'kingdom_name', 'kingdom_chemont_id',
        'superclass_name', 'superclass_chemont_id',
        'class_name', 'class_chemont_id',
        'subclass_name', 'subclass_chemont_id',
        'molecular_framework_name', 'molecular_framework_chemont_id',
        'direct_parent_name', 'direct_parent_chemont_id',
        'alternative_parents_names', 'substituents_names',
        'canonical_smiles', 'inchikey', 'molecular_formula',
        'identifier', 'classification_status'
    ]
    
    # 初始化结果列
    for col in classification_columns:
        if col not in df.columns:
            df[col] = None
    
    # 批量处理
    total_compounds = len(df_subset)
    processed = 0
    success_count = 0
    
    logger.info(f"开始处理 {total_compounds} 个化合物...")
    
    for idx, row in df_subset.iterrows():
        smiles = row[smiles_column]
        
        logger.info(f"处理化合物 {processed + 1}/{total_compounds} (行 {idx}): {smiles}")
        
        # 进行分类
        classification = classifier.classify_compound(smiles, max_retries=2, delay=batch_delay)
        
        if classification and 'error' not in classification:
            # 填充分类结果
            for col in classification_columns[:-1]:  # 除了status列
                if col in classification:
                    df.at[idx, col] = classification[col]
            df.at[idx, 'classification_status'] = 'Success'
            success_count += 1
            logger.info(f"分类成功: {classification.get('kingdom_name', 'Unknown')}")
        else:
            error_msg = classification.get('error', 'Unknown error') if classification else 'Failed'
            df.at[idx, 'classification_status'] = f'Failed: {error_msg}'
            logger.warning(f"分类失败: {error_msg}")
        
        processed += 1
        
        # 添加延迟以避免过于频繁的API调用
        if processed < total_compounds:
            logger.info(f"等待 {batch_delay} 秒...")
            time.sleep(batch_delay)
        
        # 每处理10个化合物保存一次中间结果
        if processed % 10 == 0:
            try:
                df.to_excel(output_file.replace('.xlsx', f'_temp_{processed}.xlsx'), index=False)
                logger.info(f"保存中间结果: {processed}/{total_compounds}")
            except Exception as e:
                logger.warning(f"保存中间结果失败: {str(e)}")
    
    # 保存最终结果
    try:
        df.to_excel(output_file, index=False)
        logger.info(f"结果已保存到: {output_file}")
        
        # 打印统计信息
        logger.info(f"分类统计: {success_count}/{total_compounds} 成功 ({success_count/total_compounds*100:.1f}%)")
        
    except Exception as e:
        logger.error(f"保存文件失败: {str(e)}")

# 测试函数
def test_api_connection():
    """
    测试API连接和单个化合物分类
    """
    classifier = ClassyFireClassifier()
    
    # 测试简单的化合物
    test_smiles = [
        "CCO",  # 乙醇
        "CC(=O)O",  # 醋酸
        "c1ccccc1",  # 苯
    ]
    
    for smiles in test_smiles:
        logger.info(f"\n{'='*50}")
        logger.info(f"测试 SMILES: {smiles}")
        classifier.test_single_compound(smiles)
        time.sleep(5)  # 测试间隔

# 使用示例
if __name__ == "__main__":
    # 首先测试API连接
    print("测试API连接...")
    test_api_connection()
    
    # 然后进行批量分类（建议先测试少量数据）
    INPUT_FILE = "IECSC_WangHB.xlsx"  # 输入文件路径
    OUTPUT_FILE = "IECSC_WangHB_classified_compounds.xlsx"  # 输出文件路径
    SMILES_COLUMN = "smiles"  # SMILES列名，根据实际情况修改
    
    # 执行分类（建议先处理少量数据测试）
    classify_excel_compounds(
        input_file=INPUT_FILE,
        output_file=OUTPUT_FILE,
        smiles_column=SMILES_COLUMN,
        batch_delay=5.0,  # 增加API调用间隔时间
        start_row=0,      # 从第0行开始
        max_rows=5        # 先测试5行数据
    )
    
    print("分类完成！")