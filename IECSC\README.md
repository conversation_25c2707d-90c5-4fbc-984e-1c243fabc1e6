# ClassyFire化学品分类工具

## 功能
使用ClassyFire API对化学品进行自动分类，支持批量提交、监控和结果导出。具有断点续传功能，可以处理大量化合物的分类任务。

## 文件说明

- `classyfire_main.py`: 主程序，包含完整的分类流程
- `classyfire_monitor.py`: 监控脚本，用于检查查询状态和收集结果
- `config.py`: 配置文件，包含所有可配置参数
- `example_usage.py`: 使用示例脚本
- `requirements.txt`: 依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备输入文件
Excel文件，包含`smiles`列：
```
smiles
CCO
c1ccccc1
CC(=O)O
CN1C=NC2=C1C(=O)N(C(=O)N2C)C
```

### 2. 修改配置
在`config.py`中修改配置参数：
```python
INPUT_FILE = 'your_input.xlsx'      # 输入文件
OUTPUT_FILE = 'your_output.xlsx'    # 输出文件
SMILES_COLUMN = 'smiles'            # SMILES列名
BATCH_SIZE = 50                     # 每批处理数量
SUBMIT_DELAY = 5.0                  # 提交间隔时间（秒）
```

### 3. 运行程序
```bash
# 运行完整分类流程
python classyfire_main.py

# 或者分步骤运行：
# 1. 仅检查查询状态
python classyfire_monitor.py monitor

# 2. 仅收集结果
python classyfire_monitor.py collect
```

### 4. 查看示例
```bash
python example_usage.py
```

## 工作流程

1. **提交阶段**：
   - 读取Excel文件中的SMILES
   - 过滤无效和过长的SMILES（>2000字符）
   - 分批提交到ClassyFire（默认每批50个，间隔5秒）
   - 保存查询ID到状态文件

2. **监控阶段**：
   - 定期检查查询状态（每分钟检查一次）
   - 收集已完成查询的结果
   - 更新状态文件

3. **导出阶段**：
   - 整合所有分类结果
   - 保存到Excel文件，包含原始数据和分类信息

## 输出格式

输出文件包含原始数据和以下分类结果：
- `canonical_smiles`: 标准SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式
- `kingdom_name/id`: 界名称/ID
- `superclass_name/id`: 超类名称/ID
- `class_name/id`: 类名称/ID
- `subclass_name/id`: 亚类名称/ID

## 断点续传

程序具有完善的断点续传功能：
- 所有状态信息保存在`classyfire_status.pkl`文件中
- 包括已提交的查询ID、完成状态、收集的结果等
- 程序中断后重新运行会自动从断点继续
- 不会重复提交已完成的查询

## API限制和注意事项

### ClassyFire API限制
- 每分钟最多12个POST请求
- 每个查询最多包含1000个化合物
- 结果以分页形式返回（每页10个结果）
- 分类需要时间，复杂化合物可能需要几分钟到几小时

### 使用建议
- 建议每批处理50-100个化合物
- 提交间隔设置为5秒以上
- 过长的SMILES（>2000字符）会被自动跳过
- 网络不稳定时程序会自动重试
- 大量数据建议分多次处理

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时自动重试
- API错误自动记录和跳过
- 无效SMILES自动过滤
- 详细的日志记录便于调试

## 日志文件

- `classyfire.log`: 主程序日志
- `classyfire_monitor.log`: 监控脚本日志

## 故障排除

### 常见问题

1. **提交失败**
   - 检查网络连接
   - 确认SMILES格式正确
   - 减少批处理大小

2. **查询长时间未完成**
   - ClassyFire服务器可能繁忙
   - 复杂化合物需要更长时间
   - 可以继续等待或联系ClassyFire支持

3. **结果不完整**
   - 使用监控脚本检查状态
   - 手动收集剩余结果
   - 检查日志文件了解详情

### 手动操作

如需手动操作，可以：
```python
from classyfire_main import ClassyFireBatchProcessor

# 创建处理器
processor = ClassyFireBatchProcessor()

# 检查状态
status = processor.load_status()
print(status)

# 手动收集结果
results = processor._monitor_and_collect_results(status['query_ids'], status)
```

## 引用

如果使用此工具，请引用ClassyFire原始论文：

Djoumbou Feunang Y, Eisner R, Knox C, Chepelev L, Hastings J, Owen G, Fahy E, Steinbeck C, Subramanian S, Bolton E, Greiner R, and Wishart DS. ClassyFire: Automated Chemical Classification With A Comprehensive, Computable Taxonomy. Journal of Cheminformatics, 2016, 8:61.

## 许可证

本工具基于MIT许可证开源。ClassyFire是免费服务，请遵守其使用条款。
