# ClassyFire化学品分类工具

## 功能
使用ClassyFire API对化学品进行自动分类，采用分离式设计：提交和收集功能完全独立，使用更加灵活。

## 文件说明

- `run_submit.py`: 提交脚本，配置参数后直接运行
- `run_collect.py`: 收集脚本，支持状态检查和结果收集
- `quick_start.py`: 交互式界面，适合新手使用
- `requirements.txt`: 依赖包列表

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：直接运行（推荐）

#### 1. 提交查询
1. 打开 `run_submit.py`，修改配置参数
2. 运行：`python run_submit.py`

#### 2. 检查状态（可选）
1. 打开 `run_collect.py`，设置 `CHECK_STATUS_ONLY = True`
2. 运行：`python run_collect.py`

#### 3. 收集结果
1. 打开 `run_collect.py`，设置 `CHECK_STATUS_ONLY = False`
2. 修改其他配置参数
3. 运行：`python run_collect.py`

### 方法二：交互式界面（推荐新手）
```bash
python quick_start.py
```
按照提示选择操作即可。

### 输入文件格式
Excel文件需包含SMILES列：
```
smiles
CCO
c1ccccc1
CC(=O)O
```

### 配置示例
在 `run_submit.py` 中：
```python
INPUT_FILE = 'IECSC_WangHB.xlsx'        # 您的输入文件
SMILES_COLUMN = 'smiles'                # SMILES列名
BATCH_SIZE = 50                         # 每批处理数量
SUBMIT_DELAY = 5.0                      # 提交间隔秒数
```

## 详细使用步骤

### 第一步：配置并提交查询
1. 打开 `run_submit.py` 文件
2. 在配置区域修改参数：
```python
INPUT_FILE = 'IECSC_WangHB.xlsx'        # 您的输入文件
SMILES_COLUMN = 'smiles'                # SMILES列名
BATCH_SIZE = 50                         # 每批处理数量
SUBMIT_DELAY = 5.0                      # 提交间隔秒数
```
3. 运行脚本：`python run_submit.py`

### 第二步：检查状态（可选）
1. 打开 `run_collect.py` 文件
2. 在配置区域设置：
```python
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'
CHECK_STATUS_ONLY = True                              # 仅检查状态
```
3. 运行脚本：`python run_collect.py`

### 第三步：收集结果
1. 打开 `run_collect.py` 文件
2. 在配置区域修改参数：
```python
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'      # 查询ID文件
OUTPUT_FILE = 'IECSC_WangHB_classified_results.xlsx'  # 输出文件
ORIGINAL_FILE = 'IECSC_WangHB.xlsx'                   # 原始文件
WAIT_FOR_COMPLETION = True                            # 是否等待完成
CHECK_STATUS_ONLY = False                             # 收集结果模式
```
3. 运行脚本：`python run_collect.py`

## 工作流程

### 分步工作流程
1. **提交阶段** (`run_submit.py`)：
   - 读取Excel文件中的SMILES
   - 过滤无效和过长的SMILES（>2000字符）
   - 分批提交到ClassyFire服务器（默认每批50个，间隔5秒）
   - 保存查询ID到文件（txt和json两种格式）

2. **收集阶段** (`run_collect.py`)：
   - 检查查询状态
   - 收集已完成的结果
   - 等待未完成的查询（可选）
   - 保存结果到Excel文件

### 优势
- **灵活性**：可以随时中断和恢复
- **独立性**：提交和收集完全分离
- **可控性**：可以选择何时收集结果
- **透明性**：查询ID保存在文件中，便于管理

## 输出结果

输出Excel文件包含原始数据和以下分类信息：
- `canonical_smiles`: 标准化SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式
- `kingdom_name/id`: 界分类
- `superclass_name/id`: 超类分类
- `class_name/id`: 类分类
- `subclass_name/id`: 亚类分类

## 注意事项

1. **API限制**：ClassyFire有请求频率限制，请合理设置延迟时间
2. **处理时间**：分类需要时间，复杂化合物可能需要几小时
3. **批处理大小**：建议每批50-100个化合物
4. **网络稳定性**：确保网络连接稳定，程序会自动重试失败的请求
5. **数据备份**：建议备份原始数据和查询ID文件

## 故障排除

### 网络问题
- 确保网络连接正常
- ClassyFire服务器可能暂时不可用
- 增加延迟时间减少请求频率

### 数据问题
- 检查SMILES格式是否正确
- 过长的SMILES会被自动跳过
- 无效的SMILES会被过滤

### 程序问题
- 查看日志文件了解详细错误信息
- 检查配置参数是否正确
- 确认输入文件路径和格式

## 工作流程

### 分离式设计优势
- **提交和收集完全独立**：可以随时中断和恢复
- **灵活的时间安排**：可以选择合适的时间收集结果
- **透明的状态管理**：查询ID保存在文件中，便于管理
- **简单的操作流程**：每个脚本功能单一，易于使用

### 具体流程
1. **提交阶段** (`classyfire_submit.py`)：
   - 读取Excel文件中的SMILES
   - 过滤无效和过长的SMILES（>2000字符）
   - 分批提交到ClassyFire（默认每批50个，间隔5秒）
   - 保存查询ID到文件（txt和json格式）

2. **收集阶段** (`classyfire_collect.py`)：
   - 检查查询状态
   - 收集已完成的结果
   - 等待未完成的查询（可选）
   - 保存结果到Excel文件

## 输出格式

输出文件包含原始数据和以下分类结果：
- `canonical_smiles`: 标准SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式
- `kingdom_name/id`: 界名称/ID
- `superclass_name/id`: 超类名称/ID
- `class_name/id`: 类名称/ID
- `subclass_name/id`: 亚类名称/ID

## 状态管理

### 查询ID文件
提交成功后会生成两个文件：
- `文件名_query_ids.txt`: 简单的查询ID列表
- `文件名_query_info.json`: 详细的查询信息（包含原始文件、参数等）

### 灵活的恢复机制
- 查询ID保存在文件中，不会丢失
- 可以随时使用这些文件收集结果
- 支持部分收集：已完成的查询会立即收集，未完成的会等待
- 可以选择是否等待未完成的查询

## API限制和注意事项

### ClassyFire API限制
- 每分钟最多12个POST请求
- 每个查询最多包含1000个化合物
- 结果以分页形式返回（每页10个结果）
- 分类需要时间，复杂化合物可能需要几分钟到几小时

### 使用建议
- 建议每批处理50-100个化合物
- 提交间隔设置为5秒以上
- 过长的SMILES（>2000字符）会被自动跳过
- 网络不稳定时程序会自动重试
- 大量数据建议分多次处理

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时自动重试
- API错误自动记录和跳过
- 无效SMILES自动过滤
- 详细的日志记录便于调试

## 日志文件

- `classyfire.log`: 主程序日志
- `classyfire_monitor.log`: 监控脚本日志

## 故障排除

### 常见问题

1. **提交失败**
   - 检查网络连接
   - 确认SMILES格式正确
   - 减少批处理大小

2. **查询长时间未完成**
   - ClassyFire服务器可能繁忙
   - 复杂化合物需要更长时间
   - 可以继续等待或联系ClassyFire支持

3. **结果不完整**
   - 使用监控脚本检查状态
   - 手动收集剩余结果
   - 检查日志文件了解详情

### 手动操作

如需手动操作，可以：
```python
from classyfire_main import ClassyFireBatchProcessor

# 创建处理器
processor = ClassyFireBatchProcessor()

# 检查状态
status = processor.load_status()
print(status)

# 手动收集结果
results = processor._monitor_and_collect_results(status['query_ids'], status)
```

## 引用

如果使用此工具，请引用ClassyFire原始论文：

Djoumbou Feunang Y, Eisner R, Knox C, Chepelev L, Hastings J, Owen G, Fahy E, Steinbeck C, Subramanian S, Bolton E, Greiner R, and Wishart DS. ClassyFire: Automated Chemical Classification With A Comprehensive, Computable Taxonomy. Journal of Cheminformatics, 2016, 8:61.

## 许可证

本工具基于MIT许可证开源。ClassyFire是免费服务，请遵守其使用条款。
