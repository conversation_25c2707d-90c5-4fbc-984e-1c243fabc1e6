# ClassyFire化学品分类工具

## 功能
批量提交SMILES到ClassyFire进行化学品分类，自动监控进度并导出结果。

## 使用方法

### 1. 准备输入文件
Excel文件，包含`smiles`列：
```
smiles
CCO
c1ccccc1
CC(=O)O
```

### 2. 修改配置
在`classyfire.py`的`main()`函数中修改：
```python
input_file = 'your_input.xlsx'      # 输入文件
output_file = 'your_output.xlsx'    # 输出文件
smiles_column = 'smiles'            # SMILES列名
```

### 3. 运行程序
```bash
python classyfire.py
```

## 工作流程

1. **提交阶段**：批量提交SMILES到ClassyFire（每次间隔2秒）
2. **监控阶段**：定期检查分类结果（每分钟检查一次）
3. **导出阶段**：将结果保存到Excel文件

## 输出格式

输出文件包含原始数据和分类结果：
- `kingdom_name/id`: 界
- `superclass_name/id`: 超类  
- `class_name/id`: 类
- `subclass_name/id`: 亚类
- `canonical_smiles`: 标准SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式

## 断点续传

程序会自动保存进度到`classyfire_status.pkl`，中断后重新运行会从断点继续。

## 注意事项

- ClassyFire是免费服务，请合理使用
- 分类需要时间，复杂化合物可能需要几分钟到几小时
- 程序会自动处理API限制和网络错误
- 过长的SMILES（>2000字符）会被跳过
