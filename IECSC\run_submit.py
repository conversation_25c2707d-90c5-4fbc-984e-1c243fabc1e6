#!/usr/bin/env python3
"""
ClassyFire提交脚本 - 直接运行版

配置好参数后直接运行即可，无需命令行参数。
"""

# ==================== 配置区域 ====================
# 在这里修改您的配置参数

# 输入文件配置
INPUT_FILE = 'IECSC_WangHB.xlsx'        # 输入Excel文件路径
SMILES_COLUMN = 'smiles'                # SMILES列名

# 提交参数配置
BATCH_SIZE = 50                         # 每批处理的化合物数量（建议50-100）
SUBMIT_DELAY = 5.0                      # 提交间隔时间（秒，建议5秒以上）

# ==================== 程序代码 ====================

import requests
import pandas as pd
import time
import json
import os
import logging
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classyfire_submit.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ClassyFireSubmitter:
    """ClassyFire提交器"""
    
    def __init__(self, batch_size: int = 50, delay: float = 5.0):
        self.base_url = "http://classyfire.wishartlab.com"
        self.batch_size = batch_size
        self.delay = delay
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0',
            'Content-Type': 'application/json'
        })
        
    def submit_query(self, structures: List[str], label: str = None) -> Optional[int]:
        """提交分类查询"""
        try:
            if not label:
                label = f"Batch_Classification_{int(time.time())}"
                
            query_data = {
                "label": label,
                "query_input": "\n".join(structures),
                "query_type": "STRUCTURE"
            }
            
            url = f"{self.base_url}/queries.json"
            response = self.session.post(url, json=query_data, timeout=30)
            
            if response.status_code == 201:
                result = response.json()
                query_id = result.get('id')
                logger.info(f"成功提交查询，ID: {query_id}, 标签: {label}")
                return query_id
            else:
                logger.error(f"提交查询失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"提交查询时发生错误: {e}")
            return None
    
    def process_excel_file(self, input_file: str, smiles_column: str = 'smiles') -> List[int]:
        """处理Excel文件并提交所有SMILES"""
        try:
            # 检查文件是否存在
            if not os.path.exists(input_file):
                logger.error(f"输入文件不存在: {input_file}")
                return []
            
            # 读取输入文件
            logger.info(f"读取输入文件: {input_file}")
            df = pd.read_excel(input_file)
            
            if smiles_column not in df.columns:
                logger.error(f"未找到SMILES列: {smiles_column}")
                logger.info(f"可用列: {list(df.columns)}")
                return []
            
            # 过滤有效的SMILES
            original_count = len(df)
            df = df.dropna(subset=[smiles_column])
            df = df[df[smiles_column].str.len() <= 2000]  # 过滤过长的SMILES
            
            logger.info(f"原始数据: {original_count} 行")
            logger.info(f"有效SMILES: {len(df)} 个")
            
            if len(df) == 0:
                logger.error("没有有效的SMILES数据")
                return []
            
            # 分批提交
            smiles_list = df[smiles_column].tolist()
            query_ids = []
            total_batches = (len(smiles_list) + self.batch_size - 1) // self.batch_size
            
            logger.info(f"开始分批提交，共 {total_batches} 批")
            
            for i in range(total_batches):
                start_idx = i * self.batch_size
                end_idx = min((i + 1) * self.batch_size, len(smiles_list))
                batch_smiles = smiles_list[start_idx:end_idx]
                
                logger.info(f"提交第 {i+1}/{total_batches} 批，包含 {len(batch_smiles)} 个化合物")
                
                # 提交查询
                label = f"Batch_{i+1}_{os.path.basename(input_file)}_{int(time.time())}"
                query_id = self.submit_query(batch_smiles, label)
                
                if query_id:
                    query_ids.append(query_id)
                    logger.info(f"成功提交查询 {query_id}")
                else:
                    logger.error(f"提交第 {i+1} 批失败")
                    break
                
                # 延迟以避免API限制
                if i < total_batches - 1:
                    logger.info(f"等待 {self.delay} 秒...")
                    time.sleep(self.delay)
            
            # 保存查询ID到文件
            if query_ids:
                base_name = os.path.splitext(input_file)[0]
                
                # 保存为文本文件
                query_file = f"{base_name}_query_ids.txt"
                with open(query_file, 'w') as f:
                    for qid in query_ids:
                        f.write(f"{qid}\n")
                logger.info(f"查询ID已保存到: {query_file}")
                
                # 保存为JSON文件，包含更多信息
                query_info = {
                    'input_file': input_file,
                    'smiles_column': smiles_column,
                    'total_compounds': len(smiles_list),
                    'batch_size': self.batch_size,
                    'query_ids': query_ids,
                    'submit_time': time.time(),
                    'submit_time_readable': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                json_file = f"{base_name}_query_info.json"
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(query_info, f, indent=2, ensure_ascii=False)
                logger.info(f"查询信息已保存到: {json_file}")
            
            return query_ids
            
        except Exception as e:
            logger.error(f"处理文件时发生错误: {e}")
            return []

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("ClassyFire化学品分类 - 提交脚本")
    logger.info("=" * 60)
    
    # 显示配置信息
    logger.info("当前配置:")
    logger.info(f"  输入文件: {INPUT_FILE}")
    logger.info(f"  SMILES列: {SMILES_COLUMN}")
    logger.info(f"  批处理大小: {BATCH_SIZE}")
    logger.info(f"  提交延迟: {SUBMIT_DELAY}秒")
    logger.info("-" * 60)
    
    # 创建提交器
    submitter = ClassyFireSubmitter(batch_size=BATCH_SIZE, delay=SUBMIT_DELAY)
    
    # 处理文件
    query_ids = submitter.process_excel_file(INPUT_FILE, SMILES_COLUMN)
    
    # 显示结果
    logger.info("=" * 60)
    if query_ids:
        logger.info(f"✓ 提交完成！共提交 {len(query_ids)} 个查询")
        logger.info(f"查询ID: {query_ids}")
        logger.info("")
        logger.info("下一步:")
        logger.info("1. 等待ClassyFire处理（可能需要几分钟到几小时）")
        logger.info("2. 运行 run_collect.py 收集结果")
        logger.info("3. 或使用 quick_start.py 检查状态")
    else:
        logger.error("✗ 提交失败！请检查配置和网络连接")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
