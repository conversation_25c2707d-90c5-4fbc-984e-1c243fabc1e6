{"cells": [{"cell_type": "code", "execution_count": 9, "id": "2270f0d1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [81.0, 100 - 81.0]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'FishAT'\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($\\boldsymbol{F}_{\\mathbf{sub-1}}$)',\n", "    r'Ether ($\\boldsymbol{F}_{\\mathbf{sub-2}}$)',\n", "    r'<PERSON><PERSON> ($\\boldsymbol{F}_{\\mathbf{sub-3}}$)',\n", "    r'Amine ($\\boldsymbol{F}_{\\mathbf{sub-4}}$)',\n", "    # r'Ether ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [62.7, 34.1, 34.0, 33.1, ]\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#377eb8'  # 深青色\n", "color_light_blue = '#a6cee3' # 浅青色\n", "# color_dark_blue = '#01665e'  # 深青色\n", "# color_light_blue = '#c7eae5' # 浅青色\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "plt.rcParams['font.weight'] = 'bold'  # 设置全局字体加粗\n", "# 设置数学字体也使用Times New Roman并加粗\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman:bold'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic:bold'\n", "plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "# 在圆环中心添加文字（加粗）\n", "ax1.text(0, 0, center_text, ha='center', va='center', \n", "         fontsize=font_center_text_size, fontname=font_name, fontweight='bold')\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "# 创建加粗的图例\n", "legend_font = FontProperties(family=font_name, size=font_main_label_size, weight='bold')\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=legend_font)\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "# 设置 y 轴标签的字体大小和加粗\n", "for tick in ax2.get_yticklabels():\n", "    tick.set_fontsize(font_main_label_size)\n", "    tick.set_fontweight('bold')\n", "ax2.tick_params(axis='y', length=0) # length=0 隐藏刻度线\n", "# 在每个条形图的右侧添加数值标签（加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name,\n", "             fontweight='bold')    # 加粗\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 2, "id": "edafa6f1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [72.8, 100 - 72.8]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'FishCT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Halogen ($F_{\\mathrm{sub-2}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-3}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-4}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [75.4, 43.2, 30.2, 27.4, 26.8]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#6a51a3'  # 深青色\n", "color_light_blue = '#bcbddc' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 14  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 12       # 如 63.7%\n", "font_center_text_size = 20 # 圆环中心 F_BA 的大小\n", "font_legend_size = 12      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "03f42300", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [60.2, 100 - 60.2]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'DMCT'\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($\\boldsymbol{F}_{\\mathbf{sub-1}}$)',\n", "    r'Amine ($\\boldsymbol{F}_{\\mathbf{sub-2}}$)',\n", "    r'Ether ($\\boldsymbol{F}_{\\mathbf{sub-3}}$)',\n", "    r'Alcohol ($\\boldsymbol{F}_{\\mathbf{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [62.7, 33.8, 29.6, 28.5]\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#d95f0e'  # 深青色\n", "color_light_blue = '#fdd0a2' # 浅青色\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "plt.rcParams['font.weight'] = 'bold'  # 设置全局字体加粗\n", "# 设置数学字体也使用Times New Roman并加粗\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman:bold'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic:bold'\n", "plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "# 在圆环中心添加文字（加粗）\n", "ax1.text(0, 0, center_text, ha='center', va='center', \n", "         fontsize=font_center_text_size, fontname=font_name, fontweight='bold')\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "# 创建加粗的图例\n", "legend_font = FontProperties(family=font_name, size=font_main_label_size, weight='bold')\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=legend_font)\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "# 设置 y 轴标签的字体大小和加粗\n", "for tick in ax2.get_yticklabels():\n", "    tick.set_fontsize(font_main_label_size)\n", "    tick.set_fontweight('bold')\n", "ax2.tick_params(axis='y', length=0) # length=0 隐藏刻度线\n", "# 在每个条形图的右侧添加数值标签（加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name,\n", "             fontweight='bold')    # 加粗\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "fdf904d6", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [87.9, 100 - 87.9]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'DMAT'\n", "\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($F_{\\mathrm{sub-1}}$)',\n", "    r'Amine ($F_{\\mathrm{sub-2}}$)',\n", "    r'Ether ($F_{\\mathrm{sub-3}}$)',\n", "    r'Alcohol ($F_{\\mathrm{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [57.8, 35.2, 34.4, 25.6]\n", "\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#238b45'  # 深青色\n", "color_light_blue = '#a1d99b' # 浅青色\n", "\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 14  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 12       # 如 63.7%\n", "font_center_text_size = 20 # 圆环中心 F_BA 的大小\n", "font_legend_size = 12      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "\n", "\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "# 设置数学字体也使用Times New Roman\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic'\n", "\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "\n", "# 在圆环中心添加文字\n", "ax1.text(0, 0, center_text, ha='center', va='center', fontsize=font_center_text_size, fontname=font_name)\n", "\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=FontProperties(family=font_name, size=font_main_label_size))\n", "\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "\n", "\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "\n", "# 设置 y 轴标签的字体大小\n", "ax2.tick_params(axis='y', labelsize=font_main_label_size, length=0) # length=0 隐藏刻度线\n", "\n", "# 在每个条形图的右侧添加数值标签\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name)\n", "\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "68a067d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.font_manager import FontProperties\n", "# --- 1. 自定义区域 ---\n", "# 在这里修改所有你想改变的数值、标签和颜色\n", "# --- 左侧圆环图（Donut Chart）数据 ---\n", "donut_values = [89.9, 100 - 89.9]  # [深色部分, 浅色部分] 的百分比\n", "donut_labels = ['Toxic', 'Non Toxic']\n", "center_text = r'AlgAT'\n", "# --- 右侧条形图（Bar Chart）数据 ---\n", "# 注意：为了从上到下显示，数据请按降序排列\n", "bar_labels = [\n", "    r'Aromatic Ring ($\\boldsymbol{F}_{\\mathbf{sub-1}}$)',\n", "    r'Amine ($\\boldsymbol{F}_{\\mathbf{sub-2}}$)',\n", "    r'Alcohol ($\\boldsymbol{F}_{\\mathbf{sub-3}}$)',\n", "    r'Ether ($\\boldsymbol{F}_{\\mathbf{sub-4}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-5}}$)',\n", "    # r'Alcohol ($F_{\\mathrm{sub-6}}$)'\n", "]\n", "bar_values = [57.3, 37.2, 32.5, 29.5]\n", "# --- 颜色自定义 ---\n", "# 您可以使用颜色名称（如 'skyblue'）或十六进制代码（如 '#87CEEB'）\n", "color_dark_blue = '#01665e'  # 深青色\n", "color_light_blue = '#c7eae5' # 浅青色\n", "# --- 字体和大小自定义 ---\n", "font_name = 'Times New Roman'\n", "font_main_label_size = 18  # 如 <PERSON><PERSON><PERSON>, Halogen...\n", "font_value_size = 18       # 如 63.7%\n", "font_center_text_size = 26 # 圆环中心 F_BA 的大小\n", "font_legend_size = 16      # 图例文字大小（未直接使用，图例用 main_label 大小）\n", "# --- 2. 绘图代码 ---\n", "# 设置全局字体\n", "plt.rcParams['font.family'] = font_name\n", "plt.rcParams['font.weight'] = 'bold'  # 设置全局字体加粗\n", "# 设置数学字体也使用Times New Roman并加粗\n", "plt.rcParams['mathtext.fontset'] = 'custom'\n", "plt.rcParams['mathtext.rm'] = 'Times New Roman:bold'\n", "plt.rcParams['mathtext.it'] = 'Times New Roman:italic:bold'\n", "plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'\n", "# 创建一个 1行2列 的图纸，并设置左右两部分宽度的比例\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 5), gridspec_kw={'width_ratios': [1, 2]})\n", "fig.patch.set_facecolor('white') # 设置背景为白色\n", "# --- 绘制左侧的圆环图 ---\n", "# 绘制饼图，添加间隙创建断开效果\n", "wedges, texts = ax1.pie(\n", "    donut_values,\n", "    colors=[color_dark_blue, color_light_blue],\n", "    startangle=90,          # 从90度位置开始绘制\n", "    counterclock=False,     # 顺时针绘制\n", "    wedgeprops=dict(width=0.3), # 设置环的宽度\n", "    explode=(0.05, 0.05)    # 添加间隙，创建断开效果\n", ")\n", "# 在圆环中心添加文字（加粗）\n", "ax1.text(0, 0, center_text, ha='center', va='center', \n", "         fontsize=font_center_text_size, fontname=font_name, fontweight='bold')\n", "# 创建图例（向右微移）\n", "legend_elements = [\n", "    plt.Rectangle((0, 0), 1, 1, color=color_dark_blue, label=donut_labels[0]),\n", "    plt.Rectangle((0, 0), 1, 1, color=color_light_blue, label=donut_labels[1])\n", "]\n", "# 创建加粗的图例\n", "legend_font = FontProperties(family=font_name, size=font_main_label_size, weight='bold')\n", "ax1.legend(handles=legend_elements, loc='lower left', bbox_to_anchor=(0.05, -0.05),\n", "           frameon=False, prop=legend_font)\n", "# 保持圆环是正圆形\n", "ax1.axis('equal')\n", "# --- 绘制右侧的水平条形图 ---\n", "# 由于 barh 是从下往上画的，我们需要将数据反转\n", "bar_labels.reverse()\n", "bar_values.reverse()\n", "# 绘制水平条形图（保持厚度不变）\n", "bars = ax2.barh(bar_labels, bar_values, color=color_dark_blue, height=0.3)\n", "# 移除图表的边框（spines）\n", "ax2.spines['top'].set_visible(False)\n", "ax2.spines['right'].set_visible(False)\n", "ax2.spines['bottom'].set_visible(False)\n", "ax2.spines['left'].set_visible(False)\n", "# 移除 x 轴的刻度\n", "ax2.xaxis.set_ticks_position('none')\n", "ax2.set_xticklabels([])\n", "# 设置 y 轴标签的字体大小和加粗\n", "for tick in ax2.get_yticklabels():\n", "    tick.set_fontsize(font_main_label_size)\n", "    tick.set_fontweight('bold')\n", "ax2.tick_params(axis='y', length=0) # length=0 隐藏刻度线\n", "# 在每个条形图的右侧添加数值标签（加粗）\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    ax2.text(width + 1.5,          # x 位置，在条形图右侧留出一点空隙\n", "             bar.get_y() + bar.get_height() / 2, # y 位置，垂直居中\n", "             f'{width}%',          # 显示的文本\n", "             ha='left',            # 水平对齐\n", "             va='center',          # 垂直对齐\n", "             fontsize=font_value_size,\n", "             fontname=font_name,\n", "             fontweight='bold')    # 加粗\n", "# 调整布局，防止标签重叠\n", "plt.tight_layout(pad=2.0)\n", "# 显示图表\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "2777b0b4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "56a357bb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 5}