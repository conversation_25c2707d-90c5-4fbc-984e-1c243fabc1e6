import pandas as pd
from rdkit import Chem
from collections import Counter
import sys
from tqdm import tqdm

# 告知 tqdm 如何与 pandas 协作，以显示进度条
tqdm.pandas(desc="Tagging Molecules")

# --- 1. 定义子结构 ---
# 字典的顺序不影响多标签分类，但好的组织结构便于阅读
SUBSTRUCTURE_DEFINITIONS = {
    # =======================================================
    # 主要官能团 (Functional Groups)
    # =======================================================
    'Carboxylic Acid': ('[CX3](=O)[OX2H1]', '羧基'),
    'Amide':           ('[C](=[O])[N]', '酰胺基'),
    'Ester':           ('[#6][CX3](=O)[OX1][#6]', '酯基'),
    'Sulfonamide':     ('[S](=[O])(=[O])N', '磺酰胺基'),
    'Aldehyde':        ('[CX3H1](=O)', '醛基'),
    'Ketone':          ('[#6][C](=[O])[#6]', '酮羰基'),
    'Nitro Compound':  ('[$([NX3](=O)=O),$([NX3+](=O)[O-])]', '硝基'),
    'Amine':           ('[NX3;H2,H1,H0;!$(N=O);!$(N-C=O)]', '氨基'),
    'Phenol':          ('[c][OH1]', '酚羟基'),
    'Alcohol':         ('[#6;!c][OH1]', '醇羟基'),
    'Ether':           ('[OD2]([#6])[#6]', '醚键'),
    'Thiol':           ('[#6][SH]', '硫醇基'),
    
    # =======================================================
    # 结构骨架或特征 (Structural Scaffolds / Features)
    # =======================================================
    # 'Benzene Ring':    ('c1ccccc1', '苯环'), # <-- 根据您的要求，已注释掉此行，不再识别独立的苯环
    'Indole Ring':     ('c1c2c(cn1)cccc2', '吲哚环'),
    'Pyridine Ring':   ('c1ncccc1', '吡啶环'),
    'Aromatic Ring':   ('a', '芳香环'), # 保留更通用的芳香环定义
    
    # =======================================================
    # 广义化学类别 (Broad Chemical Classes)
    # =======================================================
    'Halogen Compound': ('[F,Cl,Br,I]', '卤代物'),
}

# 预编译 SMARTS 模式以提高效率
COMPILED_PATTERNS = {name: Chem.MolFromSmarts(value[0]) for name, value in SUBSTRUCTURE_DEFINITIONS.items()}


def tag_molecule_features(smiles: str) -> str:
    """
    【多标签】检查所有预定义规则，为分子打上所有匹配的标签。
    返回一个用分号(; )分隔的字符串。
    """
    if not isinstance(smiles, str) or smiles.strip() == '':
        return 'Invalid_SMILES'
        
    mol = Chem.MolFromSmiles(smiles)

    if mol is None:
        return 'Invalid_SMILES'
    
    found_tags = []
    # 遍历所有规则，不因找到一个就停止
    for name, pattern in COMPILED_PATTERNS.items():
        if mol.HasSubstructMatch(pattern):
            found_tags.append(name)
    
    if not found_tags:
        return 'Unclassified'
    else:
        # 将所有找到的标签用分号和空格连接成一个字符串
        return '; '.join(found_tags)

# --- 用户配置区域 ---
if __name__ == "__main__":
    # 1. 设置输入文件名
    INPUT_FILE = 'AlgAT_test_for_interpretation.csv'

    # 2. 指定包含SMILES的列的名称
    SMILES_COLUMN_NAME = 'smiles'

    # 3. 设置输出文件的文件名
    OUTPUT_FILE = 'AlgAT_test_for_interpretation_with_categories.csv'

    # --- 执行脚本 ---
    print(f"--- 开始分子多标签分类任务 ---")
    
    # 1. 读取数据
    try:
        print(f"正在读取输入文件: '{INPUT_FILE}'...")
        df = pd.read_csv(INPUT_FILE)
    except FileNotFoundError:
        print(f"错误: 输入文件 '{INPUT_FILE}' 未找到。请检查文件名和路径。")
        sys.exit(1)

    # 2. 检查SMILES列是否存在
    if SMILES_COLUMN_NAME not in df.columns:
        print(f"错误: 在文件中找不到名为 '{SMILES_COLUMN_NAME}' 的列。")
        print(f"可用列: {list(df.columns)}")
        sys.exit(1)

    # 3. 应用打标签函数 (使用.progress_apply()来显示进度条)
    print(f"正在对 {len(df)} 个分子进行标记...")
    df['set'] = df[SMILES_COLUMN_NAME].progress_apply(tag_molecule_features)
    
    # 4. 保存结果
    try:
        print(f"\n标记完成。正在将结果保存到: '{OUTPUT_FILE}'...")
        # 使用 encoding='utf-8-sig' 确保在Excel中打开中文不会乱码
        df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig')
        print("文件已成功保存！")
    except Exception as e:
        print(f"保存文件时发生错误: {e}")
        sys.exit(1)
        
    # 5. (可选) 显示分类结果的统计信息
    print("\n--- 标签出现频率统计 ---")
    # 展开所有标签并计数
    all_tags = df['set'].str.split('; ').explode()
    print(all_tags.value_counts())
    print("--------------------------")