# ClassyFire使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 准备数据
确保您的Excel文件包含SMILES列，例如：
```
smiles
CCO
c1ccccc1
CC(=O)O
```

### 3. 运行分类
```bash
# 使用默认配置运行
python run_classyfire.py run

# 或指定自定义参数
python run_classyfire.py run --input your_data.xlsx --output results.xlsx
```

## 详细使用方法

### 配置文件
编辑`config.py`文件来修改默认设置：
- `INPUT_FILE`: 输入Excel文件路径
- `OUTPUT_FILE`: 输出Excel文件路径
- `SMILES_COLUMN`: SMILES列名
- `BATCH_SIZE`: 每批处理的化合物数量（建议50-100）
- `SUBMIT_DELAY`: 提交间隔时间（秒，建议5秒以上）

### 命令行选项
```bash
# 运行完整分类流程
python run_classyfire.py run

# 检查已提交查询的状态
python run_classyfire.py monitor

# 收集已完成查询的结果
python run_classyfire.py collect

# 运行测试
python run_classyfire.py test

# 显示帮助
python run_classyfire.py help
```

### 自定义参数
```bash
python run_classyfire.py run \
    --input my_compounds.xlsx \
    --output classified_results.xlsx \
    --column smiles_column \
    --batch-size 50 \
    --delay 5
```

## 工作流程

### 第一次运行
1. 程序读取Excel文件中的SMILES
2. 过滤无效和过长的SMILES
3. 分批提交到ClassyFire服务器
4. 保存查询ID到状态文件
5. 开始监控查询状态
6. 收集完成的结果并保存

### 断点续传
如果程序中断：
1. 重新运行相同命令
2. 程序自动加载之前的状态
3. 跳过已提交的查询
4. 继续监控未完成的查询
5. 收集新的结果

### 分步执行
您也可以分步执行：
```bash
# 1. 首次运行（提交查询后可能会中断）
python run_classyfire.py run

# 2. 稍后检查状态
python run_classyfire.py monitor

# 3. 收集结果
python run_classyfire.py collect
```

## 输出结果

输出Excel文件包含：
- 原始数据的所有列
- `canonical_smiles`: 标准化SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式
- `kingdom_name/id`: 界分类
- `superclass_name/id`: 超类分类
- `class_name/id`: 类分类
- `subclass_name/id`: 亚类分类

## 故障排除

### 网络问题
- 确保网络连接正常
- ClassyFire服务器可能暂时不可用
- 增加延迟时间减少请求频率

### 数据问题
- 检查SMILES格式是否正确
- 过长的SMILES会被自动跳过
- 无效的SMILES会被过滤

### 程序问题
- 运行测试检查基本功能：`python run_classyfire.py test`
- 查看日志文件了解详细错误信息
- 删除状态文件重新开始：`rm classyfire_status.pkl`

## 注意事项

1. **API限制**：ClassyFire有请求频率限制，请合理设置延迟时间
2. **处理时间**：分类需要时间，复杂化合物可能需要几小时
3. **批处理大小**：建议每批50-100个化合物
4. **网络稳定性**：确保网络连接稳定，程序会自动重试失败的请求
5. **数据备份**：建议备份原始数据和状态文件

## 示例

查看`example_usage.py`文件了解更多使用示例：
```bash
python example_usage.py
```
