# ClassyFire使用指南

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 准备数据
确保您的Excel文件包含SMILES列，例如：
```
smiles
CCO
c1ccccc1
CC(=O)O
```

### 3. 直接运行（推荐）

#### 第一步：配置并提交查询
1. 打开 `run_submit.py` 文件
2. 在配置区域修改参数：
```python
INPUT_FILE = 'IECSC_WangHB.xlsx'        # 您的输入文件
SMILES_COLUMN = 'smiles'                # SMILES列名
BATCH_SIZE = 50                         # 每批处理数量
SUBMIT_DELAY = 5.0                      # 提交间隔秒数
```
3. 运行脚本：
```bash
python run_submit.py
```

#### 第二步：检查状态（可选）
1. 打开 `check_status.py` 文件
2. 确认查询ID文件路径：
```python
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'
```
3. 运行脚本：
```bash
python check_status.py
```

#### 第三步：收集结果
1. 打开 `run_collect.py` 文件
2. 在配置区域修改参数：
```python
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'      # 查询ID文件
OUTPUT_FILE = 'IECSC_WangHB_classified_results.xlsx'  # 输出文件
ORIGINAL_FILE = 'IECSC_WangHB.xlsx'                   # 原始文件
WAIT_FOR_COMPLETION = True                            # 是否等待完成
```
3. 运行脚本：
```bash
python run_collect.py
```

## 详细使用方法

### 方法一：直接运行脚本（推荐）

所有参数都在脚本内部配置，无需命令行参数：

#### 1. 提交脚本 (run_submit.py)
- 打开文件，修改配置区域的参数
- 直接运行：`python run_submit.py`
- 生成查询ID文件供后续使用

#### 2. 状态检查脚本 (check_status.py)
- 打开文件，确认查询ID文件路径
- 直接运行：`python check_status.py`
- 快速查看所有查询的状态

#### 3. 收集脚本 (run_collect.py)
- 打开文件，修改配置区域的参数
- 直接运行：`python run_collect.py`
- 自动收集结果并保存

### 方法二：命令行方式（备用）

如果您喜欢命令行方式，也可以使用：

```bash
# 提交查询
python classyfire_submit.py IECSC_WangHB.xlsx smiles 50 5

# 检查状态
python classyfire_collect.py status IECSC_WangHB_query_ids.txt

# 收集结果
python classyfire_collect.py IECSC_WangHB_query_ids.txt results.xlsx IECSC_WangHB.xlsx
```

### 方法三：交互式界面
```bash
python quick_start.py
```
提供友好的菜单界面，适合不熟悉命令行的用户。

## 工作流程

### 分步工作流程（推荐）

#### 第一阶段：提交查询
1. `classyfire_submit.py` 读取Excel文件中的SMILES
2. 过滤无效和过长的SMILES（>2000字符）
3. 分批提交到ClassyFire服务器（默认每批50个，间隔5秒）
4. 保存查询ID到文件（txt和json两种格式）
5. 提交完成，可以关闭程序

#### 第二阶段：监控和收集
1. 使用 `classyfire_collect.py status` 检查查询状态
2. 使用 `classyfire_collect.py` 收集结果：
   - 自动监控所有查询状态
   - 收集已完成的结果
   - 等待未完成的查询（每分钟检查一次）
   - 保存最终结果到Excel文件

### 优势
- **灵活性**：可以随时中断和恢复
- **独立性**：提交和收集完全分离
- **可控性**：可以选择何时收集结果
- **透明性**：查询ID保存在文件中，便于管理

## 输出结果

输出Excel文件包含：
- 原始数据的所有列
- `canonical_smiles`: 标准化SMILES
- `inchikey`: InChI Key
- `molecular_formula`: 分子式
- `kingdom_name/id`: 界分类
- `superclass_name/id`: 超类分类
- `class_name/id`: 类分类
- `subclass_name/id`: 亚类分类

## 故障排除

### 网络问题
- 确保网络连接正常
- ClassyFire服务器可能暂时不可用
- 增加延迟时间减少请求频率

### 数据问题
- 检查SMILES格式是否正确
- 过长的SMILES会被自动跳过
- 无效的SMILES会被过滤

### 程序问题
- 运行测试检查基本功能：`python run_classyfire.py test`
- 查看日志文件了解详细错误信息
- 删除状态文件重新开始：`rm classyfire_status.pkl`

## 注意事项

1. **API限制**：ClassyFire有请求频率限制，请合理设置延迟时间
2. **处理时间**：分类需要时间，复杂化合物可能需要几小时
3. **批处理大小**：建议每批50-100个化合物
4. **网络稳定性**：确保网络连接稳定，程序会自动重试失败的请求
5. **数据备份**：建议备份原始数据和状态文件

## 示例

查看`example_usage.py`文件了解更多使用示例：
```bash
python example_usage.py
```
