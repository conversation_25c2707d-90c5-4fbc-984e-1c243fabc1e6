#!/usr/bin/env python3
"""
ClassyFire状态检查脚本 - 直接运行版

配置好参数后直接运行即可，快速检查查询状态。
"""

# ==================== 配置区域 ====================
# 在这里修改您的配置参数

# 查询ID文件配置（提交脚本生成的文件）
QUERY_IDS_FILE = 'IECSC_WangHB_query_ids.txt'      # 查询ID文件路径
# 或者使用JSON文件: 'IECSC_WangHB_query_info.json'

# ==================== 程序代码 ====================

import requests
import json
import os
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClassyFireStatusChecker:
    """ClassyFire状态检查器"""
    
    def __init__(self):
        self.base_url = "http://classyfire.wishartlab.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'ClassyFire-Python-Client/1.0'
        })
    
    def get_query_status(self, query_id: int) -> Optional[Dict]:
        """获取查询状态"""
        try:
            url = f"{self.base_url}/queries/{query_id}.json"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取查询 {query_id} 状态时发生错误: {e}")
            return None
    
    def check_all_status(self, query_ids: List[int]) -> Dict[int, Dict]:
        """检查所有查询的状态"""
        results = {}
        
        logger.info(f"检查 {len(query_ids)} 个查询的状态...")
        logger.info("=" * 80)
        
        for i, query_id in enumerate(query_ids, 1):
            query_status = self.get_query_status(query_id)
            
            if query_status:
                status_value = query_status.get('classification_status', 'Unknown')
                num_entities = query_status.get('number_of_elements', 0)
                label = query_status.get('label', '')
                
                results[query_id] = {
                    'status': status_value,
                    'num_entities': num_entities,
                    'label': label
                }
                
                # 状态显示
                status_icon = {
                    'Done': '✓',
                    'In Queue': '⏳',
                    'Processing': '🔄',
                    'Failed': '✗',
                    'Timed out': '⏰'
                }.get(status_value, '?')
                
                logger.info(f"{i:2d}. 查询 {query_id}: {status_icon} {status_value} ({num_entities} 个化合物)")
                if label:
                    logger.info(f"     标签: {label}")
            else:
                results[query_id] = {
                    'status': 'Error',
                    'num_entities': 0,
                    'label': ''
                }
                logger.info(f"{i:2d}. 查询 {query_id}: ✗ 无法获取状态")
        
        return results
    
    def show_summary(self, results: Dict[int, Dict]):
        """显示状态摘要"""
        logger.info("=" * 80)
        logger.info("状态摘要:")
        
        # 统计各种状态
        status_count = {}
        total_entities = 0
        
        for query_id, info in results.items():
            status = info['status']
            status_count[status] = status_count.get(status, 0) + 1
            total_entities += info['num_entities']
        
        # 显示统计
        for status, count in sorted(status_count.items()):
            icon = {
                'Done': '✓',
                'In Queue': '⏳',
                'Processing': '🔄',
                'Failed': '✗',
                'Timed out': '⏰',
                'Error': '❌'
            }.get(status, '?')
            
            logger.info(f"  {icon} {status}: {count} 个查询")
        
        logger.info(f"  📊 总化合物数: {total_entities}")
        
        # 计算完成率
        completed = status_count.get('Done', 0)
        total = len(results)
        completion_rate = (completed / total * 100) if total > 0 else 0
        
        logger.info(f"  📈 完成率: {completed}/{total} ({completion_rate:.1f}%)")
        
        # 给出建议
        logger.info("=" * 80)
        if completion_rate == 100:
            logger.info("🎉 所有查询已完成！可以运行 run_collect.py 收集结果")
        elif completion_rate > 0:
            logger.info("⏳ 部分查询已完成，可以运行 run_collect.py 收集已完成的结果")
            logger.info("   或等待所有查询完成后再收集")
        else:
            logger.info("⏳ 查询仍在处理中，请稍后再检查")
        
        logger.info("=" * 80)

def load_query_ids(query_file: str) -> List[int]:
    """从文件加载查询ID"""
    if not os.path.exists(query_file):
        logger.error(f"查询ID文件不存在: {query_file}")
        return []
    
    query_ids = []
    
    if query_file.endswith('.json'):
        # JSON格式
        try:
            with open(query_file, 'r', encoding='utf-8') as f:
                query_info = json.load(f)
                query_ids = query_info.get('query_ids', [])
                
                # 显示提交信息
                submit_time = query_info.get('submit_time_readable', '')
                total_compounds = query_info.get('total_compounds', 0)
                if submit_time:
                    logger.info(f"提交时间: {submit_time}")
                if total_compounds:
                    logger.info(f"总化合物数: {total_compounds}")
                    
        except Exception as e:
            logger.error(f"读取JSON文件失败: {e}")
    else:
        # 文本格式
        try:
            with open(query_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and line.isdigit():
                        query_ids.append(int(line))
        except Exception as e:
            logger.error(f"读取文本文件失败: {e}")
    
    return query_ids

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("ClassyFire化学品分类 - 状态检查")
    logger.info("=" * 80)
    
    # 显示配置信息
    logger.info(f"查询ID文件: {QUERY_IDS_FILE}")
    logger.info("-" * 80)
    
    # 加载查询ID
    query_ids = load_query_ids(QUERY_IDS_FILE)
    
    if not query_ids:
        logger.error("未找到有效的查询ID，请先运行 run_submit.py")
        return
    
    logger.info(f"找到 {len(query_ids)} 个查询ID: {query_ids}")
    logger.info("")
    
    # 创建状态检查器
    checker = ClassyFireStatusChecker()
    
    # 检查状态
    results = checker.check_all_status(query_ids)
    
    # 显示摘要
    checker.show_summary(results)

if __name__ == "__main__":
    main()
