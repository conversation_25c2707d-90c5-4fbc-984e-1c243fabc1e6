import sys
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem.Draw import rdMolDraw2D
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import matplotlib.colors as mcolors
import io
from PIL import Image

def visualize_molecule_with_weights(smiles, atom_weights, file_name, exp_val=None, pre_val=None):
    """
    Generates and saves a visualization of a molecule with atoms colored by weight.

    Args:
        smiles (str): The SMILES string of the molecule.
        atom_weights (list): A list of weights for each heavy atom.
        file_name (str): The path to save the output image file.
        exp_val (float, optional): Experimental value to display.
        pre_val (float, optional): Predicted value to display.
    """
    mol = Chem.MolFromSmiles(smiles)
    if not mol:
        print(f"Error: Invalid SMILES string '{smiles}'", file=sys.stderr)
        return

    # Add explicit hydrogens for better visualization, then generate 2D coordinates
    mol = Chem.AddHs(mol)
    AllChem.Compute2DCoords(mol)

    # --- Sanity Check ---
    # Ensure the number of weights matches the number of heavy atoms
    num_heavy_atoms = len([atom for atom in mol.GetAtoms() if atom.GetAtomicNum() != 1])
    if len(atom_weights) != num_heavy_atoms:
        print(f"Error: Number of weights ({len(atom_weights)}) does not match number of heavy atoms ({num_heavy_atoms}).", file=sys.stderr)
        return

    # --- Color Mapping ---
    # Create a color map (e.g., 'Blues', 'Reds', 'viridis')
    cmap = cm.get_cmap('Blues')
    # Normalize weights to the range [0, 1] for the color map
    norm = mcolors.Normalize(vmin=min(atom_weights), vmax=max(atom_weights))

    # Map heavy atom indices and their colors
    heavy_atom_indices = [atom.GetIdx() for atom in mol.GetAtoms() if atom.GetAtomicNum() != 1]
    atom_colors = {}
    for i, atom_idx in enumerate(heavy_atom_indices):
        weight = atom_weights[i]
        atom_colors[atom_idx] = cmap(norm(weight))

    # --- Drawing the Molecule ---
    drawer = rdMolDraw2D.MolDraw2DCairo(600, 500) # Width, Height
    draw_options = drawer.drawOptions()
    draw_options.bondLineWidth = 2
    draw_options.addAtomIndices = False # Set to True to see atom indices
    
    # Use continuous highlighting for a smoother look like in your example
    draw_options.continuousHighlight = True
    draw_options.highlightBondWidthMultiplier = 16
    draw_options.highlightRadius = 0.35

    # Draw the molecule with highlighted atoms and bonds
    drawer.DrawMolecule(
        mol,
        highlightAtoms=list(atom_colors.keys()),
        highlightAtomColors=atom_colors,
        highlightBonds=[] # No bond highlights for now
    )
    drawer.FinishDrawing()
    png_data = drawer.GetDrawingText()

    # --- Creating the Final Figure with Matplotlib ---
    fig, ax = plt.subplots(figsize=(7, 6))
    
    # Display molecule image
    mol_img = Image.open(io.BytesIO(png_data))
    ax.imshow(mol_img)
    ax.axis('off')

    # Add SMILES and other text below the image
    # Note: You can replace 'N/A' if you have the exp/pre values.
    fig.text(0.5, 0.18, f"SMILES: {smiles}", ha='center', va='center', fontsize=10, wrap=True)
    if exp_val is not None:
        fig.text(0.5, 0.12, f"exp: {exp_val:.2f}", ha='center', va='center', fontsize=12)
    if pre_val is not None:
        fig.text(0.5, 0.07, f"pre: {pre_val:.2f}", ha='center', va='center', fontsize=12)

    # --- Adding the Color Bar ---
    cax = fig.add_axes([0.88, 0.3, 0.03, 0.4]) # [left, bottom, width, height]
    scalar_mappable = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    cbar = plt.colorbar(scalar_mappable, cax=cax)
    cbar.set_label('Attention weight (ω)', fontsize=12, labelpad=15)
    
    # Save the final figure
    plt.savefig(file_name, bbox_inches='tight', dpi=300)
    print(f"Visualization saved to '{file_name}'")
    plt.close()


# ==============================================================================
# --- YOUR DATA HERE ---
# ==============================================================================

# 1. SMILES string of the molecule
my_smiles = "C1CCC(NC2CCCCC2)CC1"

# 2. List of atom weights extracted from your image (in order of atom_idx 0 to 12)
my_atom_weights = [
    0.6364111,   # atom_idx 0
    0.67344254,  # atom_idx 1
    0.6572559,   # atom_idx 2
    0.5423504,   # atom_idx 3
    0.40864173,  # atom_idx 4 (The Nitrogen atom)
    0.5423504,   # atom_idx 5
    0.6572559,   # atom_idx 6
    0.67344254,  # atom_idx 7
    0.6364111,   # atom_idx 8
    0.67344254,  # atom_idx 9
    0.6572559,   # atom_idx 10
    0.6572559,   # atom_idx 11
    0.67344254   # atom_idx 12
]

# 3. (Optional) Experimental and predicted values
# Since these were not in your spreadsheet, I'm leaving them as None.
# You can fill them in like this: experimental_value = 2.50
experimental_value = None
predicted_value = None

# 4. Define the output file name
output_file = f"molecule_{my_smiles}.png"

# 5. Call the function to generate the image
visualize_molecule_with_weights(
    smiles=my_smiles,
    atom_weights=my_atom_weights,
    file_name=output_file,
    exp_val=experimental_value,
    pre_val=predicted_value
)